@extends('admin.layouts.admin')

@section('title', __('messages.edit_purchase'))

@push('styles')
<style>
    .product-row {
        transition: all 0.3s ease;
    }
    .product-row:hover {
        background-color: #f8fafc;
    }
    .remove-btn:hover {
        transform: scale(1.1);
    }
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">{{ __('messages.edit_purchase') }}</h1>
            <p class="text-gray-600 mt-1">{{ __('Edit purchase order') }} #{{ $purchase->purchase_number }}</p>
        </div>
        <div class="flex space-x-3 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
            <a href="{{ route('admin.transactions.purchases.show', $purchase) }}" class="bg-emerald-500 hover:bg-emerald-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {{ __('messages.view') }}
            </a>
            <a href="{{ route('admin.transactions.purchases.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                {{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Purchase Form -->
    <form action="{{ route('admin.transactions.purchases.update', $purchase) }}" method="POST" id="purchaseForm">
        @csrf
        @method('PUT')
        
        <!-- Purchase Information -->
        <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50 mb-6">
            <div class="bg-gradient-to-r from-indigo-500 to-blue-600 px-6 py-4">
                <h3 class="text-lg font-bold text-white">{{ __('Purchase Information') }}</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="supplier_id" class="block text-sm font-semibold text-gray-700 mb-2">{{ __('messages.supplier') }} <span class="text-red-500">*</span></label>
                        <select name="supplier_id" id="supplier_id" required 
                                class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-500/20 transition-all duration-200">
                            <option value="">{{ __('Select Supplier') }}</option>
                            @foreach($suppliers ?? [] as $supplier)
                                <option value="{{ $supplier->id }}" {{ (old('supplier_id', $purchase->supplier_id) == $supplier->id) ? 'selected' : '' }}>
                                    {{ $supplier->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('supplier_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="purchase_date" class="block text-sm font-semibold text-gray-700 mb-2">{{ __('Purchase Date') }} <span class="text-red-500">*</span></label>
                        <input type="date" name="purchase_date" id="purchase_date" value="{{ old('purchase_date', $purchase->purchase_date->format('Y-m-d')) }}" required
                               class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-500/20 transition-all duration-200">
                        @error('purchase_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-semibold text-gray-700 mb-2">{{ __('messages.notes') }}</label>
                        <textarea name="notes" id="notes" rows="3" placeholder="{{ __('Additional notes for this purchase...') }}"
                                  class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-indigo-500 focus:ring-4 focus:ring-indigo-500/20 transition-all duration-200">{{ old('notes', $purchase->notes) }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Items -->
        <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50 mb-6">
            <div class="bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-bold text-white">{{ __('Purchase Items') }}</h3>
                <button type="button" id="addItemBtn" class="bg-white/20 hover:bg-white/30 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center">
                    <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {{ __('Add Item') }}
                </button>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-gray-200">
                                <th class="text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} py-3 text-sm font-semibold text-gray-700">{{ __('messages.product') }}</th>
                                <th class="text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} py-3 text-sm font-semibold text-gray-700">{{ __('messages.quantity') }}</th>
                                <th class="text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} py-3 text-sm font-semibold text-gray-700">{{ __('Unit Price') }}</th>
                                <th class="text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} py-3 text-sm font-semibold text-gray-700">{{ __('messages.total') }}</th>
                                <th class="text-center py-3 text-sm font-semibold text-gray-700">{{ __('messages.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody id="itemsContainer">
                            <!-- Items will be added here dynamically -->
                        </tbody>
                    </table>
                </div>
                
                @error('items')
                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                @enderror
                
                <!-- Total Section -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex justify-end">
                        <div class="w-64">
                            <div class="flex justify-between items-center py-2 text-lg font-bold text-gray-900">
                                <span>{{ __('Grand Total') }}:</span>
                                <span id="grandTotal">$0.00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
            <a href="{{ route('admin.transactions.purchases.show', $purchase) }}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-8 rounded-xl transition duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                {{ __('messages.cancel') }}
            </a>
            <button type="submit" 
                    class="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-xl transition duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                {{ __('messages.update_purchase') }}
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
let itemIndex = 0;
const products = @json($products ?? []);
const existingItems = @json($purchase->items ?? []);

// Add new item row
document.getElementById('addItemBtn').addEventListener('click', function() {
    addItemRow();
});

function addItemRow(productId = '', quantity = '', unitPrice = '') {
    const container = document.getElementById('itemsContainer');
    const row = document.createElement('tr');
    row.className = 'product-row border-b border-gray-100';
    row.innerHTML = `
        <td class="py-3 pr-4">
            <select name="items[${itemIndex}][product_id]" class="product-select w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20" required>
                <option value="">{{ __('Select Product') }}</option>
                ${products.map(product => `
                    <option value="${product.id}" ${productId == product.id ? 'selected' : ''} data-price="${product.selling_price || 0}">
                        ${product.name} (${product.category?.name || ''}) - ${product.unit?.symbol || ''}
                    </option>
                `).join('')}
            </select>
        </td>
        <td class="py-3 pr-4">
            <input type="number" name="items[${itemIndex}][quantity]" value="${quantity}" step="0.01" min="0.01" 
                   class="quantity-input w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20" 
                   placeholder="0.00" required>
        </td>
        <td class="py-3 pr-4">
            <input type="number" name="items[${itemIndex}][unit_price]" value="${unitPrice}" step="0.01" min="0" 
                   class="unit-price-input w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20" 
                   placeholder="0.00" required>
        </td>
        <td class="py-3 pr-4">
            <span class="total-price text-lg font-semibold text-gray-900">$0.00</span>
        </td>
        <td class="py-3 text-center">
            <button type="button" class="remove-btn text-red-600 hover:text-red-800 transition-colors duration-200" onclick="removeItem(this)">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </td>
    `;
    
    container.appendChild(row);
    
    // Add event listeners
    const productSelect = row.querySelector('.product-select');
    const quantityInput = row.querySelector('.quantity-input');
    const unitPriceInput = row.querySelector('.unit-price-input');
    
    productSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.dataset.price) {
            unitPriceInput.value = selectedOption.dataset.price;
            calculateRowTotal(row);
        }
    });
    
    quantityInput.addEventListener('input', () => calculateRowTotal(row));
    unitPriceInput.addEventListener('input', () => calculateRowTotal(row));
    
    // Calculate initial total
    calculateRowTotal(row);
    
    itemIndex++;
}

function removeItem(button) {
    button.closest('tr').remove();
    calculateGrandTotal();
}

function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price-input').value) || 0;
    const total = quantity * unitPrice;
    
    row.querySelector('.total-price').textContent = `$${total.toFixed(2)}`;
    calculateGrandTotal();
}

function calculateGrandTotal() {
    let grandTotal = 0;
    document.querySelectorAll('.total-price').forEach(element => {
        const value = parseFloat(element.textContent.replace('$', '')) || 0;
        grandTotal += value;
    });
    
    document.getElementById('grandTotal').textContent = `$${grandTotal.toFixed(2)}`;
}

// Load existing items
existingItems.forEach(item => {
    addItemRow(item.product_id, item.quantity, item.unit_price);
});

// Add initial row if no existing items
if (existingItems.length === 0) {
    addItemRow();
}

// Form validation
document.getElementById('purchaseForm').addEventListener('submit', function(e) {
    const items = document.querySelectorAll('#itemsContainer tr');
    if (items.length === 0) {
        e.preventDefault();
        alert('{{ __("Please add at least one item to the purchase.") }}');
        return false;
    }
    
    let hasValidItems = false;
    items.forEach(row => {
        const productSelect = row.querySelector('.product-select');
        const quantity = row.querySelector('.quantity-input');
        const unitPrice = row.querySelector('.unit-price-input');
        
        if (productSelect.value && quantity.value && unitPrice.value) {
            hasValidItems = true;
        }
    });
    
    if (!hasValidItems) {
        e.preventDefault();
        alert('{{ __("Please fill in all required fields for at least one item.") }}');
        return false;
    }
});
</script>
@endpush
@endsection
