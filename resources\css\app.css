@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic Font Configuration */
@layer base {
    /* Apply <PERSON>wal font for Arabic content */
    html[lang="ar"] {
        font-family: "<PERSON>jawal", "Inter", ui-sans-serif, system-ui, -apple-system,
            BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
            "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
            "Segoe UI Symbol", "Noto Color Emoji";
    }

    html[lang="ar"] body {
        font-family: "Tajawal", "Inter", ui-sans-serif, system-ui, -apple-system,
            BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
            "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
            "Segoe UI Symbol", "Noto Color Emoji";
    }

    /* Ensure proper font rendering for Arabic text */
    html[lang="ar"] * {
        font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Specific font weights for Arabic */
    html[lang="ar"] .font-light {
        font-weight: 300;
    }
    html[lang="ar"] .font-normal {
        font-weight: 400;
    }
    html[lang="ar"] .font-medium {
        font-weight: 500;
    }
    html[lang="ar"] .font-semibold {
        font-weight: 600;
    }
    html[lang="ar"] .font-bold {
        font-weight: 700;
    }
    html[lang="ar"] .font-extrabold {
        font-weight: 800;
    }
    html[lang="ar"] .font-black {
        font-weight: 900;
    }
}

/* Arabic Font Utility Classes */
@layer utilities {
    .font-arabic {
        font-family: "Tajawal", "Inter", ui-sans-serif, system-ui, -apple-system,
            BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
            "Noto Sans", sans-serif;
    }

    .font-inter {
        font-family: "Inter", ui-sans-serif, system-ui, -apple-system,
            BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
            "Noto Sans", sans-serif;
    }

    /* Arabic text optimization */
    .text-arabic {
        font-family: "Tajawal", "Inter", ui-sans-serif, system-ui, -apple-system,
            BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial,
            "Noto Sans", sans-serif;
        font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Modern Sidebar Styles */
@layer components {
    /* Legacy sidebar styles for compatibility */
    .sidebar-link {
        @apply flex items-center px-4 py-3 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200;
    }

    .sidebar-link.active {
        @apply bg-blue-600 text-white;
    }

    .sidebar-link svg {
        @apply mr-3 flex-shrink-0;
    }

    .sidebar-section-header {
        @apply flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200;
    }

    .sidebar-submenu {
        @apply mt-1 space-y-1 pl-4;
    }

    .sidebar-sublink {
        @apply flex items-center px-4 py-2 text-sm text-gray-400 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200;
    }

    .sidebar-sublink.active {
        @apply bg-blue-500 text-white;
    }

    /* Modern Navigation Styles */
    .modern-nav-link {
        @apply relative flex items-center px-4 py-3 text-sm font-medium text-gray-600 rounded-xl hover:bg-gray-100 hover:text-gray-900 transition-all duration-300;
    }

    .modern-nav-link.active {
        @apply bg-blue-600 text-white shadow-lg;
    }

    .modern-nav-link.active .nav-indicator {
        @apply opacity-100;
    }

    .nav-icon-wrapper {
        @apply w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center mr-2 transition-all duration-300;
    }

    .modern-nav-link:hover .nav-icon-wrapper {
        @apply bg-gray-200;
    }

    .modern-nav-link.active .nav-icon-wrapper {
        @apply bg-white bg-opacity-20;
    }

    .modern-nav-link.active .nav-icon-wrapper svg {
        @apply text-white;
    }

    .nav-text {
        @apply flex-1 font-medium;
    }

    .nav-indicator {
        @apply absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-l-full opacity-0 transition-opacity duration-300;
    }

    /* Modern Section Styles */
    .modern-nav-section {
        @apply mb-2;
    }

    .modern-section-header {
        @apply flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-600 rounded-xl hover:bg-gray-100 hover:text-gray-900 transition-all duration-300;
    }

    .modern-submenu {
        @apply mt-2 space-y-1 pl-6 border-l border-gray-300 ml-6;
    }

    .modern-sublink {
        @apply relative flex items-center px-4 py-2 text-sm text-gray-500 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-all duration-300;
    }

    .modern-sublink.active {
        @apply bg-blue-500 text-white;
    }

    .sublink-icon {
        @apply w-6 h-6 rounded-md bg-gray-100 flex items-center justify-center mr-2 transition-all duration-300;
    }

    .modern-sublink.active .sublink-icon {
        @apply bg-white bg-opacity-20;
    }

    .modern-sublink.active .sublink-icon svg {
        @apply text-white;
    }
}

/* RTL Support */
[dir="rtl"] .sidebar-link svg {
    @apply ml-2 mr-0;
}

[dir="rtl"] .nav-icon-wrapper {
    @apply ml-2 mr-0;
}

[dir="rtl"] .sublink-icon {
    @apply ml-2 mr-0;
}

[dir="rtl"] .sidebar-submenu {
    @apply pr-4 pl-0;
}

/* Custom animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

/* Additional utility classes */
.rtl {
    direction: rtl;
}

.ltr {
    direction: ltr;
}
