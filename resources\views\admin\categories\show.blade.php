@extends('admin.layouts.admin')

@section('title', __('messages.view') . ' ' . __('messages.category'))

@section('content')
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-900">{{ __('messages.category') }}: {{ $category->name }}</h1>
            <div class="flex space-x-2 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
                <a href="{{ route('admin.master.categories.edit', $category) }}"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center">
                    <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    {{ __('messages.edit') }}
                </a>
                <a href="{{ route('admin.master.categories.index') }}"
                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center">
                    <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    {{ __('Back') }}
                </a>
            </div>
        </div>

        <!-- Category Details -->
        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Basic Information') }}</h3>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('messages.name') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $category->name }}</p>
                        </div>

                        @if ($category->description)
                            <div>
                                <label
                                    class="block text-sm font-medium text-gray-700">{{ __('messages.description') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $category->description }}</p>
                            </div>
                        @endif

                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('Parent Category') }}</label>
                            @if ($category->parent)
                                <div class="mt-1">
                                    <a href="{{ route('admin.master.categories.show', $category->parent) }}"
                                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition duration-200">
                                        {{ $category->parent->name }}
                                    </a>
                                </div>
                            @else
                                <p class="mt-1 text-sm text-gray-500">{{ __('This is a parent category') }}</p>
                            @endif
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('messages.status') }}</label>
                            <div class="mt-1">
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $category->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $category->is_active ? __('messages.active') : __('messages.inactive') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('Statistics') }}</h3>

                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                        </svg>
                                    </div>
                                    <div class="{{ app()->getLocale() === 'ar' ? 'mr-3' : 'ml-3' }}">
                                        <p class="text-sm font-medium text-blue-600">{{ __('messages.products') }}</p>
                                        <p class="text-2xl font-bold text-blue-900">{{ $category->products->count() }}</p>
                                    </div>
                                </div>
                            </div>

                            @if ($category->children->count() > 0)
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                                                </path>
                                            </svg>
                                        </div>
                                        <div class="{{ app()->getLocale() === 'ar' ? 'mr-3' : 'ml-3' }}">
                                            <p class="text-sm font-medium text-green-600">{{ __('Subcategories') }}</p>
                                            <p class="text-2xl font-bold text-green-900">{{ $category->children->count() }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('Created') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $category->created_at->format('M d, Y H:i') }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('Last Updated') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $category->updated_at->format('M d, Y H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Subcategories -->
        @if ($category->children->count() > 0)
            <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Subcategories') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach ($category->children as $child)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition duration-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">{{ $child->name }}</h4>
                                        @if ($child->description)
                                            <p class="text-xs text-gray-500 mt-1">{{ Str::limit($child->description, 50) }}
                                            </p>
                                        @endif
                                        <p class="text-xs text-gray-400 mt-1">{{ $child->products->count() }}
                                            {{ __('messages.products') }}</p>
                                    </div>
                                    <div class="flex space-x-1 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
                                        <a href="{{ route('admin.master.categories.show', $child) }}"
                                            class="text-blue-600 hover:text-blue-900 text-xs">{{ __('messages.view') }}</a>
                                        <a href="{{ route('admin.master.categories.edit', $child) }}"
                                            class="text-green-600 hover:text-green-900 text-xs">{{ __('messages.edit') }}</a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Products -->
        @if ($category->products->count() > 0)
            <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ __('messages.products') }}</h3>
                        <a href="{{ route('admin.master.products.index') }}?category_id={{ $category->id }}"
                            class="text-blue-600 hover:text-blue-800 text-sm">{{ __('View All') }} →</a>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ __('messages.name') }}</th>
                                    <th
                                        class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        SKU</th>
                                    <th
                                        class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ __('messages.stock') }}</th>
                                    <th
                                        class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ __('messages.price') }}</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach ($category->products->take(5) as $product)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <a href="{{ route('admin.master.products.show', $product) }}"
                                                class="text-sm font-medium text-blue-600 hover:text-blue-900">{{ $product->name }}</a>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $product->sku }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {{ $product->stock_quantity }} {{ $product->unit->symbol }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            ${{ number_format($product->sale_price, 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @if ($category->products->count() > 5)
                        <div class="mt-4 text-center">
                            <a href="{{ route('admin.master.products.index') }}?category_id={{ $category->id }}"
                                class="text-blue-600 hover:text-blue-800 text-sm">{{ __('View All') }}
                                {{ $category->products->count() }} {{ __('messages.products') }} →</a>
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
@endsection
