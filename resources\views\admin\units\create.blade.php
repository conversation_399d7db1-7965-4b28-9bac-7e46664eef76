@extends('admin.layouts.admin')

@section('title', __('messages.add') . ' ' . __('messages.unit'))

@section('content')
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-900">{{ __('messages.add') }} {{ __('messages.unit') }}</h1>
            <a href="{{ route('admin.master.units.index') }}"
                class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                {{ __('Back') }}
            </a>
        </div>

        <!-- Form -->
        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <form action="{{ route('admin.master.units.store') }}" method="POST" class="space-y-6">
                    @csrf

                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">{{ __('messages.name') }}
                            <span class="text-red-500">*</span></label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-500 @enderror"
                            placeholder="{{ __('Enter unit name (e.g., Kilogram, Piece)') }}">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Symbol -->
                    <div>
                        <label for="symbol" class="block text-sm font-medium text-gray-700">{{ __('Symbol') }} <span
                                class="text-red-500">*</span></label>
                        <input type="text" name="symbol" id="symbol" value="{{ old('symbol') }}" required
                            maxlength="10"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('symbol') border-red-500 @enderror"
                            placeholder="{{ __('Enter unit symbol (e.g., kg, pcs)') }}">
                        @error('symbol')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500">
                            {{ __('Short abbreviation for the unit (max 10 characters)') }}</p>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description"
                            class="block text-sm font-medium text-gray-700">{{ __('messages.description') }}</label>
                        <textarea name="description" id="description" rows="3"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('description') border-red-500 @enderror"
                            placeholder="{{ __('Enter unit description') }}">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1"
                                {{ old('is_active', true) ? 'checked' : '' }}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active"
                                class="{{ app()->getLocale() === 'ar' ? 'mr-2' : 'ml-2' }} block text-sm text-gray-900">
                                {{ __('messages.active') }}
                            </label>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">
                            {{ __('Inactive units will not be available for selection') }}</p>
                    </div>

                    <!-- Submit Buttons -->
                    <div
                        class="flex items-center justify-end space-x-3 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
                        <a href="{{ route('admin.master.units.index') }}"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-200">
                            {{ __('messages.cancel') }}
                        </a>
                        <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center">
                            <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            {{ __('messages.save') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Common Units Reference -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="{{ app()->getLocale() === 'ar' ? 'mr-3' : 'ml-3' }}">
                    <h3 class="text-sm font-medium text-blue-800">{{ __('Common Units Reference') }}</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                            <div><strong>{{ __('Weight') }}:</strong> kg, g, lb, oz</div>
                            <div><strong>{{ __('Volume') }}:</strong> L, mL, gal, qt</div>
                            <div><strong>{{ __('Length') }}:</strong> m, cm, ft, in</div>
                            <div><strong>{{ __('Count') }}:</strong> pcs, dz, box, pack</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
