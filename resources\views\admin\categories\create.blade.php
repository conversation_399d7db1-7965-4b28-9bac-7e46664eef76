@extends('admin.layouts.admin')

@section('title', __('messages.add') . ' ' . __('messages.category'))

@section('content')
    <div class="space-y-8">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-800">
                    {{ __('messages.add') }} {{ __('messages.category') }}
                </h1>
                <p class="text-gray-600 mt-2">{{ __('Create a new category to organize your products') }}</p>
            </div>
            <a href="{{ route('admin.master.categories.index') }}"
                class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                {{ __('messages.back') }}
            </a>
        </div>

        <!-- Form -->
        <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
            <div class="p-8">
                <form action="{{ route('admin.master.categories.store') }}" method="POST" class="space-y-6">
                    @csrf

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                                    {{ __('messages.name') }} <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                    class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-200 @error('name') border-red-500 ring-4 ring-red-500/20 @enderror"
                                    placeholder="{{ __('Enter category name') }}">
                                @error('name')
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-semibold text-gray-700 mb-2">
                                    {{ __('messages.description') }}
                                </label>
                                <textarea name="description" id="description" rows="4"
                                    class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-200 @error('description') border-red-500 ring-4 ring-red-500/20 @enderror"
                                    placeholder="{{ __('Enter category description (optional)') }}">{{ old('description') }}</textarea>
                                @error('description')
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        {{ $message }}
                                    </p>
                                @enderror
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- Parent Category -->
                            <div>
                                <label for="parent_id" class="block text-sm font-semibold text-gray-700 mb-2">
                                    {{ __('Parent Category') }}
                                </label>
                                <select name="parent_id" id="parent_id"
                                    class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-200 @error('parent_id') border-red-500 ring-4 ring-red-500/20 @enderror">
                                    <option value="">{{ __('Select parent category (optional)') }}</option>
                                    @foreach ($parentCategories ?? [] as $parentCategory)
                                        <option value="{{ $parentCategory->id }}"
                                            {{ old('parent_id') == $parentCategory->id ? 'selected' : '' }}>
                                            {{ $parentCategory->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('parent_id')
                                    <p class="mt-2 text-sm text-red-600 flex items-center">
                                        <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-1' : 'mr-1' }}"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                        {{ $message }}
                                    </p>
                                @enderror
                                <p class="mt-2 text-sm text-gray-500">{{ __('Leave empty to create a parent category') }}
                                </p>
                            </div>

                            <!-- Status -->
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_active" id="is_active" value="1"
                                        {{ old('is_active', true) ? 'checked' : '' }}
                                        class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
                                    <span
                                        class="{{ app()->getLocale() === 'ar' ? 'mr-2' : 'ml-2' }} text-sm font-medium text-gray-700">{{ __('messages.active') }}</span>
                                </label>
                                <p class="mt-2 text-sm text-gray-500">
                                    {{ __('Inactive categories will not be available for selection') }}</p>
                            </div>

                            <!-- Category Tips -->
                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                                <h3 class="text-lg font-semibold text-blue-800 mb-3 flex items-center">
                                    <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ __('Category Tips') }}
                                </h3>
                                <ul class="space-y-2 text-sm text-blue-700">
                                    <li class="flex items-start">
                                        <span
                                            class="w-2 h-2 bg-blue-400 rounded-full mt-2 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }} flex-shrink-0"></span>
                                        {{ __('Use clear, descriptive names for better organization') }}
                                    </li>
                                    <li class="flex items-start">
                                        <span
                                            class="w-2 h-2 bg-blue-400 rounded-full mt-2 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }} flex-shrink-0"></span>
                                        {{ __('Parent categories help create hierarchical structure') }}
                                    </li>
                                    <li class="flex items-start">
                                        <span
                                            class="w-2 h-2 bg-blue-400 rounded-full mt-2 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }} flex-shrink-0"></span>
                                        {{ __('Descriptions help users understand category purpose') }}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div
                        class="flex items-center justify-end space-x-4 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }} pt-6 border-t border-gray-200">
                        <a href="{{ route('admin.master.categories.index') }}"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-3 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                            {{ __('messages.cancel') }}
                        </a>
                        <button type="submit"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center">
                            <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            {{ __('messages.save') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Help Section -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="{{ app()->getLocale() === 'ar' ? 'mr-3' : 'ml-3' }}">
                    <h3 class="text-sm font-medium text-blue-800">{{ __('Category Guidelines') }}</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc {{ app()->getLocale() === 'ar' ? 'list-inside' : 'pl-5' }} space-y-1">
                            <li>{{ __('Use clear and descriptive names for categories') }}</li>
                            <li>{{ __('Parent categories help organize products hierarchically') }}</li>
                            <li>{{ __('Subcategories inherit properties from their parent') }}</li>
                            <li>{{ __('Inactive categories will be hidden from product selection') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
