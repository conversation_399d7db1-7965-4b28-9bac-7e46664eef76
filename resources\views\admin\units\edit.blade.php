@extends('admin.layouts.admin')

@section('title', __('messages.edit') . ' ' . __('messages.unit'))

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">{{ __('messages.edit') }} {{ __('messages.unit') }}</h1>
            <p class="text-gray-600 mt-1">{{ __('Update unit information and settings') }}</p>
        </div>
        <a href="{{ route('admin.master.units.index') }}" class="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            {{ __('messages.back') }}
        </a>
    </div>

    <!-- Edit Form -->
    <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
        <div class="p-8">
            <form action="{{ route('admin.master.units.update', $unit) }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Unit Name -->
                <div>
                    <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">
                        {{ __('messages.unit_name') }} <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           name="name" 
                           id="name" 
                           value="{{ old('name', $unit->name) }}"
                           class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-orange-500 focus:ring-4 focus:ring-orange-500/20 transition-all duration-200 @error('name') border-red-500 ring-4 ring-red-500/20 @enderror" 
                           placeholder="{{ __('Enter unit name (e.g., Kilogram, Piece, Liter)') }}"
                           required>
                    @error('name')
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <!-- Unit Symbol -->
                <div>
                    <label for="symbol" class="block text-sm font-semibold text-gray-700 mb-2">
                        {{ __('Unit Symbol') }} <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           name="symbol" 
                           id="symbol" 
                           value="{{ old('symbol', $unit->symbol) }}"
                           class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-orange-500 focus:ring-4 focus:ring-orange-500/20 transition-all duration-200 @error('symbol') border-red-500 ring-4 ring-red-500/20 @enderror" 
                           placeholder="{{ __('Enter unit symbol (e.g., kg, pcs, L)') }}"
                           required>
                    @error('symbol')
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-semibold text-gray-700 mb-2">
                        {{ __('messages.description') }}
                    </label>
                    <textarea name="description" 
                              id="description" 
                              rows="4" 
                              class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-orange-500 focus:ring-4 focus:ring-orange-500/20 transition-all duration-200 @error('description') border-red-500 ring-4 ring-red-500/20 @enderror" 
                              placeholder="{{ __('Enter unit description (optional)') }}">{{ old('description', $unit->description) }}</textarea>
                    @error('description')
                        <p class="mt-2 text-sm text-red-600 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" 
                               name="is_active" 
                               value="1" 
                               {{ old('is_active', $unit->is_active) ? 'checked' : '' }}
                               class="w-5 h-5 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2">
                        <span class="ml-2 text-sm font-medium text-gray-700">{{ __('messages.active') }}</span>
                    </label>
                    <p class="mt-1 text-sm text-gray-500">{{ __('Inactive units will not be available for selection') }}</p>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-end space-x-4 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }} pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.master.units.index') }}" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-8 rounded-xl transition duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        {{ __('messages.cancel') }}
                    </a>
                    <button type="submit" 
                            class="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-bold py-3 px-8 rounded-xl transition duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center">
                        <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {{ __('messages.update') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Common Units Reference -->
    <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-200">
        <h3 class="text-lg font-semibold text-orange-800 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ __('Common Units Reference') }}
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <h4 class="font-semibold text-orange-700 mb-2">{{ __('Weight') }}</h4>
                <ul class="space-y-1 text-orange-600">
                    <li>• {{ __('Kilogram (kg)') }}</li>
                    <li>• {{ __('Gram (g)') }}</li>
                    <li>• {{ __('Pound (lb)') }}</li>
                    <li>• {{ __('Ounce (oz)') }}</li>
                </ul>
            </div>
            <div>
                <h4 class="font-semibold text-orange-700 mb-2">{{ __('Volume') }}</h4>
                <ul class="space-y-1 text-orange-600">
                    <li>• {{ __('Liter (L)') }}</li>
                    <li>• {{ __('Milliliter (ml)') }}</li>
                    <li>• {{ __('Gallon (gal)') }}</li>
                    <li>• {{ __('Cubic meter (m³)') }}</li>
                </ul>
            </div>
            <div>
                <h4 class="font-semibold text-orange-700 mb-2">{{ __('Count') }}</h4>
                <ul class="space-y-1 text-orange-600">
                    <li>• {{ __('Piece (pcs)') }}</li>
                    <li>• {{ __('Dozen (dz)') }}</li>
                    <li>• {{ __('Box (box)') }}</li>
                    <li>• {{ __('Pack (pack)') }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
