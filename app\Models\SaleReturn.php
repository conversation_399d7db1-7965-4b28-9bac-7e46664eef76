<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SaleReturn extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'return_number',
        'sale_id',
        'customer_id',
        'user_id',
        'return_date',
        'total_amount',
        'status',
        'reason',
        'notes',
    ];

    protected $casts = [
        'return_date' => 'date',
        'total_amount' => 'decimal:2',
    ];

    // Relationships
    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(SaleReturnItem::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Business Logic Methods
    public function calculateTotals(): void
    {
        $this->total_amount = $this->items()->sum('total_price');
        $this->save();
    }

    public function complete(): void
    {
        $this->status = 'completed';
        $this->save();

        // Update product stock (increase because items are returned)
        foreach ($this->items as $item) {
            $item->product->increaseStock($item->quantity);
        }

        // Update customer balance
        if ($this->customer) {
            $this->customer->updateBalance(-$this->total_amount);
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($return) {
            if (empty($return->return_number)) {
                $return->return_number = 'SR-' . date('Y') . '-' . str_pad(
                    static::whereYear('created_at', date('Y'))->count() + 1,
                    6,
                    '0',
                    STR_PAD_LEFT
                );
            }
        });
    }
}
