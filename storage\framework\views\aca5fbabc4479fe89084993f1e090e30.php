<?php $__env->startSection('title', __('messages.products')); ?>

<?php $__env->startSection('content'); ?>
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-900"><?php echo e(__('messages.products')); ?></h1>
            <a href="<?php echo e(route('admin.master.products.create')); ?>"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center">
                <svg class="w-5 h-5 <?php echo e(app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2'); ?>" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
                <?php echo e(__('messages.add_product')); ?>

            </a>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <form method="GET" action="<?php echo e(route('admin.master.products.index')); ?>"
                    class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search"
                            class="block text-sm font-medium text-gray-700"><?php echo e(__('messages.search')); ?></label>
                        <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                            placeholder="<?php echo e(__('Search products...')); ?>"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="category_id"
                            class="block text-sm font-medium text-gray-700"><?php echo e(__('messages.category')); ?></label>
                        <select name="category_id" id="category_id"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value=""><?php echo e(__('All Categories')); ?></option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>"
                                    <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div>
                        <label for="stock_status"
                            class="block text-sm font-medium text-gray-700"><?php echo e(__('Stock Status')); ?></label>
                        <select name="stock_status" id="stock_status"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value=""><?php echo e(__('All Stock')); ?></option>
                            <option value="in_stock" <?php echo e(request('stock_status') == 'in_stock' ? 'selected' : ''); ?>>
                                <?php echo e(__('messages.in_stock')); ?></option>
                            <option value="low_stock" <?php echo e(request('stock_status') == 'low_stock' ? 'selected' : ''); ?>>
                                <?php echo e(__('messages.low_stock')); ?></option>
                            <option value="out_of_stock" <?php echo e(request('stock_status') == 'out_of_stock' ? 'selected' : ''); ?>>
                                <?php echo e(__('messages.out_of_stock')); ?></option>
                        </select>
                    </div>
                    <div class="flex items-end space-x-2 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                        <button type="submit"
                            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                            <?php echo e(__('messages.filter')); ?>

                        </button>
                        <a href="<?php echo e(route('admin.master.products.index')); ?>"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-200">
                            <?php echo e(__('messages.clear')); ?>

                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Products Table -->
        <div class="bg-white overflow-hidden shadow-sm rounded-lg">
            <div class="p-6">
                <?php if(session('success')): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <?php echo e(session('error')); ?>

                    </div>
                <?php endif; ?>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th
                                    class="px-6 py-3 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <?php echo e(__('messages.product')); ?></th>
                                <th
                                    class="px-6 py-3 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    SKU</th>
                                <th
                                    class="px-6 py-3 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <?php echo e(__('messages.category')); ?></th>
                                <th
                                    class="px-6 py-3 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <?php echo e(__('messages.stock')); ?></th>
                                <th
                                    class="px-6 py-3 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <?php echo e(__('messages.price')); ?></th>
                                <th
                                    class="px-6 py-3 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <?php echo e(__('messages.status')); ?></th>
                                <th
                                    class="px-6 py-3 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <?php echo e(__('Actions')); ?></th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?php echo e($product->name); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo e(Str::limit($product->description, 50)); ?>

                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <span
                                            class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded"><?php echo e($product->sku); ?></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo e($product->category->name); ?>

                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo e($product->stock_quantity); ?>

                                            <?php echo e($product->unit->symbol); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e(__('Min')); ?>:
                                            <?php echo e($product->minimum_stock); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?php echo e(__('Sale')); ?>:
                                            $<?php echo e(number_format($product->sale_price, 2)); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e(__('Cost')); ?>:
                                            $<?php echo e(number_format($product->purchase_price, 2)); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span
                                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        <?php if($product->stock_quantity <= 0): ?> bg-red-100 text-red-800
                                        <?php elseif($product->is_low_stock): ?> bg-yellow-100 text-yellow-800
                                        <?php else: ?> bg-green-100 text-green-800 <?php endif; ?>">
                                            <?php echo e($product->stock_status); ?>

                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div
                                            class="flex items-center space-x-2 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                                            <a href="<?php echo e(route('admin.master.products.show', $product)); ?>"
                                                class="text-blue-600 hover:text-blue-900 transition duration-200"><?php echo e(__('messages.view')); ?></a>
                                            <a href="<?php echo e(route('admin.master.products.edit', $product)); ?>"
                                                class="text-green-600 hover:text-green-900 transition duration-200"><?php echo e(__('messages.edit')); ?></a>
                                            <form action="<?php echo e(route('admin.master.products.destroy', $product)); ?>"
                                                method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit"
                                                    class="text-red-600 hover:text-red-900 transition duration-200"
                                                    onclick="return confirm('<?php echo e(__('messages.confirm_delete')); ?>')">
                                                    <?php echo e(__('messages.delete')); ?>

                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7"
                                        class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <div class="flex flex-col items-center py-8">
                                            <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4">
                                                </path>
                                            </svg>
                                            <p class="text-gray-500"><?php echo e(__('messages.no_records_found')); ?></p>
                                            <a href="<?php echo e(route('admin.master.products.create')); ?>"
                                                class="mt-2 text-blue-600 hover:text-blue-800"><?php echo e(__('messages.add_product')); ?></a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if($products->hasPages()): ?>
                    <div class="mt-6">
                        <?php echo e($products->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\new_erd\resources\views/admin/products/index.blade.php ENDPATH**/ ?>