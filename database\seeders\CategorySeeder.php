<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            // Parent categories
            [
                'name' => 'Electronics',
                'description' => 'Electronic devices and accessories',
                'parent_id' => null,
                'is_active' => true,
            ],
            [
                'name' => 'Clothing',
                'description' => 'Apparel and fashion items',
                'parent_id' => null,
                'is_active' => true,
            ],
            [
                'name' => 'Food & Beverages',
                'description' => 'Food items and drinks',
                'parent_id' => null,
                'is_active' => true,
            ],
            [
                'name' => 'Home & Garden',
                'description' => 'Home improvement and garden supplies',
                'parent_id' => null,
                'is_active' => true,
            ],
            [
                'name' => 'Books & Media',
                'description' => 'Books, magazines, and media content',
                'parent_id' => null,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData);
        }

        // Create subcategories
        $electronics = Category::where('name', 'Electronics')->first();
        $clothing = Category::where('name', 'Clothing')->first();
        $food = Category::where('name', 'Food & Beverages')->first();

        $subcategories = [
            // Electronics subcategories
            [
                'name' => 'Smartphones',
                'description' => 'Mobile phones and accessories',
                'parent_id' => $electronics->id,
                'is_active' => true,
            ],
            [
                'name' => 'Laptops',
                'description' => 'Portable computers',
                'parent_id' => $electronics->id,
                'is_active' => true,
            ],
            [
                'name' => 'Accessories',
                'description' => 'Electronic accessories',
                'parent_id' => $electronics->id,
                'is_active' => true,
            ],
            // Clothing subcategories
            [
                'name' => 'Men\'s Clothing',
                'description' => 'Clothing for men',
                'parent_id' => $clothing->id,
                'is_active' => true,
            ],
            [
                'name' => 'Women\'s Clothing',
                'description' => 'Clothing for women',
                'parent_id' => $clothing->id,
                'is_active' => true,
            ],
            [
                'name' => 'Children\'s Clothing',
                'description' => 'Clothing for children',
                'parent_id' => $clothing->id,
                'is_active' => true,
            ],
            // Food subcategories
            [
                'name' => 'Snacks',
                'description' => 'Snack foods and treats',
                'parent_id' => $food->id,
                'is_active' => true,
            ],
            [
                'name' => 'Beverages',
                'description' => 'Drinks and beverages',
                'parent_id' => $food->id,
                'is_active' => true,
            ],
            [
                'name' => 'Fresh Produce',
                'description' => 'Fresh fruits and vegetables',
                'parent_id' => $food->id,
                'is_active' => true,
            ],
        ];

        foreach ($subcategories as $subcategoryData) {
            Category::create($subcategoryData);
        }
    }
}
