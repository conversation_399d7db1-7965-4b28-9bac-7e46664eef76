@extends('admin.layouts.admin')

@section('title', __('messages.view') . ' ' . __('messages.customer'))

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{{ __('messages.view') }} {{ __('messages.customer') }}</h1>
            <p class="text-gray-600 mt-1">{{ __('Customer details and transaction history') }}</p>
        </div>
        <div class="flex space-x-3 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
            <a href="{{ route('admin.master.customers.edit', $customer) }}" class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                {{ __('messages.edit') }}
            </a>
            <a href="{{ route('admin.master.customers.index') }}" class="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                {{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
        <div class="p-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Information -->
                <div class="space-y-6">
                    <h2 class="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-2">{{ __('Customer Information') }}</h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold text-xl shadow-lg {{ app()->getLocale() === 'ar' ? 'ml-4' : 'mr-4' }}">
                                {{ substr($customer->name, 0, 1) }}
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-gray-900">{{ $customer->name }}</h3>
                                @if($customer->company)
                                    <p class="text-gray-600">{{ $customer->company }}</p>
                                @endif
                                <p class="text-sm text-gray-500">{{ __('Customer ID') }}: #{{ $customer->id }}</p>
                            </div>
                        </div>

                        @if($customer->email)
                        <div class="flex items-center p-4 bg-gray-50 rounded-xl">
                            <svg class="w-5 h-5 text-gray-400 {{ app()->getLocale() === 'ar' ? 'ml-3' : 'mr-3' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <div>
                                <p class="text-sm font-medium text-gray-600">{{ __('messages.email') }}</p>
                                <p class="text-gray-900">{{ $customer->email }}</p>
                            </div>
                        </div>
                        @endif

                        @if($customer->phone)
                        <div class="flex items-center p-4 bg-gray-50 rounded-xl">
                            <svg class="w-5 h-5 text-gray-400 {{ app()->getLocale() === 'ar' ? 'ml-3' : 'mr-3' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <div>
                                <p class="text-sm font-medium text-gray-600">{{ __('messages.phone') }}</p>
                                <p class="text-gray-900">{{ $customer->phone }}</p>
                            </div>
                        </div>
                        @endif

                        @if($customer->address)
                        <div class="flex items-start p-4 bg-gray-50 rounded-xl">
                            <svg class="w-5 h-5 text-gray-400 {{ app()->getLocale() === 'ar' ? 'ml-3' : 'mr-3' }} mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <div>
                                <p class="text-sm font-medium text-gray-600">{{ __('messages.address') }}</p>
                                <p class="text-gray-900">{{ $customer->address }}</p>
                            </div>
                        </div>
                        @endif

                        <div class="flex items-center p-4 bg-gray-50 rounded-xl">
                            <svg class="w-5 h-5 text-gray-400 {{ app()->getLocale() === 'ar' ? 'ml-3' : 'mr-3' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-sm font-medium text-gray-600">{{ __('messages.status') }}</p>
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full {{ $customer->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $customer->is_active ? __('messages.active') : __('messages.inactive') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Information -->
                <div class="space-y-6">
                    <h2 class="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-2">{{ __('Account Information') }}</h2>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Account Balance -->
                        <div class="bg-gradient-to-br {{ $customer->account_balance > 0 ? 'from-green-500 to-emerald-600' : ($customer->account_balance < 0 ? 'from-red-500 to-pink-600' : 'from-gray-500 to-gray-600') }} rounded-2xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white/80 text-sm font-medium">{{ __('Account Balance') }}</p>
                                    <p class="text-2xl font-bold">${{ number_format(abs($customer->account_balance), 2) }}</p>
                                    <p class="text-white/80 text-xs">
                                        @if($customer->account_balance > 0)
                                            {{ __('Credit Balance') }}
                                        @elseif($customer->account_balance < 0)
                                            {{ __('Debit Balance') }}
                                        @else
                                            {{ __('Zero Balance') }}
                                        @endif
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Credit Limit -->
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-blue-100 text-sm font-medium">{{ __('Credit Limit') }}</p>
                                    <p class="text-2xl font-bold">${{ number_format($customer->credit_limit ?? 0, 2) }}</p>
                                </div>
                                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Sales Statistics -->
                        <div class="bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-orange-100 text-sm font-medium">{{ __('Total Sales') }}</p>
                                    <p class="text-2xl font-bold">${{ number_format($customer->total_sales ?? 0, 2) }}</p>
                                    <p class="text-orange-100 text-xs">{{ $customer->sales_count ?? 0 }} {{ __('transactions') }}</p>
                                </div>
                                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Sales -->
                    @if(isset($customer->sales) && $customer->sales->count() > 0)
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('Recent Sales') }}</h3>
                        <div class="space-y-2">
                            @foreach($customer->sales->take(5) as $sale)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl border border-gray-200">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $sale->sale_number }}</p>
                                    <p class="text-sm text-gray-500">{{ $sale->sale_date->format('M d, Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">${{ number_format($sale->total_amount, 2) }}</p>
                                    <span class="px-2 py-1 text-xs rounded-full {{ $sale->status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ ucfirst($sale->status) }}
                                    </span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        @if($customer->sales->count() > 5)
                        <div class="mt-3 text-center">
                            <a href="{{ route('admin.transactions.sales.index', ['customer_id' => $customer->id]) }}" class="text-blue-600 hover:text-blue-800 font-medium">
                                {{ __('View all sales') }} ({{ $customer->sales->count() - 5 }} {{ __('more') }})
                            </a>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>

            @if($customer->notes)
            <!-- Notes Section -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('Notes') }}</h3>
                <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                    <p class="text-gray-800">{{ $customer->notes }}</p>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- System Information -->
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ __('System Information') }}
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-semibold text-gray-600">{{ __('Created') }}:</span>
                <span class="text-gray-800">{{ $customer->created_at->format('M d, Y H:i') }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-600">{{ __('Last Updated') }}:</span>
                <span class="text-gray-800">{{ $customer->updated_at->format('M d, Y H:i') }}</span>
            </div>
        </div>
    </div>
</div>
@endsection
