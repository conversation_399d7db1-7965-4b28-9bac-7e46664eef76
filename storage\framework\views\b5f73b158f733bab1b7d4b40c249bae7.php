<?php $__env->startSection('title', __('messages.customers')); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent"><?php echo e(__('messages.customers')); ?></h1>
            <p class="text-gray-600 mt-1"><?php echo e(__('Manage your customer database and account balances')); ?></p>
        </div>
        <a href="<?php echo e(route('admin.master.customers.create')); ?>" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            <svg class="w-5 h-5 <?php echo e(app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2'); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <?php echo e(__('messages.add')); ?> <?php echo e(__('messages.customer')); ?>

        </a>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
        <div class="p-6">
            <form method="GET" action="<?php echo e(route('admin.master.customers.index')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search" class="block text-sm font-semibold text-gray-700 mb-2"><?php echo e(__('messages.search')); ?></label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="<?php echo e(__('Search customers...')); ?>" 
                           class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-200">
                </div>
                <div>
                    <label for="status" class="block text-sm font-semibold text-gray-700 mb-2"><?php echo e(__('messages.status')); ?></label>
                    <select name="status" id="status" 
                            class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-200">
                        <option value=""><?php echo e(__('All Customers')); ?></option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>><?php echo e(__('messages.active')); ?></option>
                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>><?php echo e(__('messages.inactive')); ?></option>
                    </select>
                </div>
                <div>
                    <label for="balance_status" class="block text-sm font-semibold text-gray-700 mb-2"><?php echo e(__('Balance Status')); ?></label>
                    <select name="balance_status" id="balance_status" 
                            class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20 transition-all duration-200">
                        <option value=""><?php echo e(__('All Balances')); ?></option>
                        <option value="positive" <?php echo e(request('balance_status') == 'positive' ? 'selected' : ''); ?>><?php echo e(__('Credit Balance')); ?></option>
                        <option value="negative" <?php echo e(request('balance_status') == 'negative' ? 'selected' : ''); ?>><?php echo e(__('Debit Balance')); ?></option>
                        <option value="zero" <?php echo e(request('balance_status') == 'zero' ? 'selected' : ''); ?>><?php echo e(__('Zero Balance')); ?></option>
                    </select>
                </div>
                <div class="flex items-end space-x-2 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                    <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <?php echo e(__('messages.filter')); ?>

                    </button>
                    <a href="<?php echo e(route('admin.master.customers.index')); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-xl transition duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        <?php echo e(__('messages.clear')); ?>

                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
        <div class="p-6">
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-xl mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                        <tr>
                            <th class="px-6 py-4 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-bold text-gray-600 uppercase tracking-wider"><?php echo e(__('messages.customer')); ?></th>
                            <th class="px-6 py-4 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-bold text-gray-600 uppercase tracking-wider"><?php echo e(__('Contact Info')); ?></th>
                            <th class="px-6 py-4 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-bold text-gray-600 uppercase tracking-wider"><?php echo e(__('Account Balance')); ?></th>
                            <th class="px-6 py-4 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-bold text-gray-600 uppercase tracking-wider"><?php echo e(__('Total Sales')); ?></th>
                            <th class="px-6 py-4 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-bold text-gray-600 uppercase tracking-wider"><?php echo e(__('messages.status')); ?></th>
                            <th class="px-6 py-4 text-<?php echo e(app()->getLocale() === 'ar' ? 'right' : 'left'); ?> text-xs font-bold text-gray-600 uppercase tracking-wider"><?php echo e(__('Actions')); ?></th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                            <?php echo e(substr($customer->name, 0, 1)); ?>

                                        </div>
                                        <div class="<?php echo e(app()->getLocale() === 'ar' ? 'mr-4' : 'ml-4'); ?>">
                                            <div class="text-sm font-semibold text-gray-900"><?php echo e($customer->name); ?></div>
                                            <?php if($customer->company): ?>
                                                <div class="text-sm text-gray-500"><?php echo e($customer->company); ?></div>
                                            <?php endif; ?>
                                            <div class="text-xs text-gray-400"><?php echo e(__('Customer ID')); ?>: <?php echo e($customer->id); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <?php if($customer->email): ?>
                                            <div class="flex items-center mb-1">
                                                <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                                </svg>
                                                <?php echo e($customer->email); ?>

                                            </div>
                                        <?php endif; ?>
                                        <?php if($customer->phone): ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                                </svg>
                                                <?php echo e($customer->phone); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-semibold <?php echo e($customer->account_balance > 0 ? 'text-green-600' : ($customer->account_balance < 0 ? 'text-red-600' : 'text-gray-600')); ?>">
                                        $<?php echo e(number_format(abs($customer->account_balance), 2)); ?>

                                        <?php if($customer->account_balance > 0): ?>
                                            <span class="text-xs text-green-500 block"><?php echo e(__('Credit')); ?></span>
                                        <?php elseif($customer->account_balance < 0): ?>
                                            <span class="text-xs text-red-500 block"><?php echo e(__('Debit')); ?></span>
                                        <?php else: ?>
                                            <span class="text-xs text-gray-500 block"><?php echo e(__('Zero')); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">$<?php echo e(number_format($customer->total_sales ?? 0, 2)); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo e($customer->sales_count ?? 0); ?> <?php echo e(__('transactions')); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($customer->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                        <?php echo e($customer->is_active ? __('messages.active') : __('messages.inactive')); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                                        <a href="<?php echo e(route('admin.master.customers.show', $customer)); ?>" class="text-blue-600 hover:text-blue-900 transition duration-200 font-medium"><?php echo e(__('messages.view')); ?></a>
                                        <a href="<?php echo e(route('admin.master.customers.edit', $customer)); ?>" class="text-green-600 hover:text-green-900 transition duration-200 font-medium"><?php echo e(__('messages.edit')); ?></a>
                                        <button onclick="deleteCustomer(<?php echo e($customer->id); ?>)" class="text-red-600 hover:text-red-900 transition duration-200 font-medium">
                                            <?php echo e(__('messages.delete')); ?>

                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="px-6 py-12 whitespace-nowrap text-sm text-gray-500 text-center">
                                    <div class="flex flex-col items-center">
                                        <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                        <p class="text-gray-500 text-lg font-medium"><?php echo e(__('messages.no_records_found')); ?></p>
                                        <p class="text-gray-400 mt-1"><?php echo e(__('Start by adding your first customer')); ?></p>
                                        <a href="<?php echo e(route('admin.master.customers.create')); ?>" class="mt-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-xl hover:shadow-lg transition duration-200"><?php echo e(__('messages.add')); ?> <?php echo e(__('messages.customer')); ?></a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($customers->hasPages()): ?>
                <div class="mt-6">
                    <?php echo e($customers->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-2xl rounded-2xl bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-2"><?php echo e(__('messages.confirm_delete')); ?></h3>
            <p class="text-sm text-gray-500 mt-2"><?php echo e(__('This action cannot be undone. This will permanently delete the customer and all related data.')); ?></p>
            <div class="items-center px-4 py-3">
                <form id="deleteForm" method="POST" class="inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="px-6 py-2 bg-red-500 text-white text-base font-medium rounded-xl shadow-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300 <?php echo e(app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2'); ?> transition duration-200">
                        <?php echo e(__('messages.delete')); ?>

                    </button>
                </form>
                <button onclick="closeDeleteModal()" class="px-6 py-2 bg-gray-500 text-white text-base font-medium rounded-xl shadow-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 transition duration-200">
                    <?php echo e(__('messages.cancel')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function deleteCustomer(id) {
    document.getElementById('deleteForm').action = `/admin/master/customers/${id}`;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\new_erd\resources\views/admin/customers/index.blade.php ENDPATH**/ ?>