<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255|unique:products,name',
            'description' => 'nullable|string|max:1000',
            'sku' => 'required|string|max:100|unique:products,sku',
            'barcode' => 'nullable|string|max:100|unique:products,barcode',
            'category_id' => 'required|exists:categories,id',
            'unit_id' => 'required|exists:units,id',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0|gte:cost_price',
            'stock_quantity' => 'required|integer|min:0',
            'min_stock_level' => 'nullable|integer|min:0',
            'max_stock_level' => 'nullable|integer|min:0|gte:min_stock_level',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => __('Product name is required.'),
            'name.unique' => __('Product name already exists.'),
            'sku.required' => __('SKU is required.'),
            'sku.unique' => __('SKU already exists.'),
            'barcode.unique' => __('Barcode already exists.'),
            'category_id.required' => __('Category is required.'),
            'category_id.exists' => __('Selected category does not exist.'),
            'unit_id.required' => __('Unit is required.'),
            'unit_id.exists' => __('Selected unit does not exist.'),
            'cost_price.required' => __('Cost price is required.'),
            'cost_price.min' => __('Cost price must be at least 0.'),
            'selling_price.required' => __('Selling price is required.'),
            'selling_price.gte' => __('Selling price must be greater than or equal to cost price.'),
            'stock_quantity.required' => __('Stock quantity is required.'),
            'stock_quantity.min' => __('Stock quantity must be at least 0.'),
            'max_stock_level.gte' => __('Maximum stock level must be greater than or equal to minimum stock level.'),
        ];
    }
}
