<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
        ]);

        // Create additional test users
        User::factory()->create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
        ]);

        User::factory()->create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
        ]);

        // Seed master data
        $this->call([
            CategorySeeder::class,
            UnitSeeder::class,
            SupplierSeeder::class,
            CustomerSeeder::class,
            ProductSeeder::class,
        ]);
    }
}
