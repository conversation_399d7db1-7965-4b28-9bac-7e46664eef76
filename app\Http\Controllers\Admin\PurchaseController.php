<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Purchase;
use App\Models\PurchaseItem;
use App\Models\Supplier;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PurchaseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Purchase::with(['supplier', 'user', 'items'])
            ->latest();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('purchase_number', 'like', "%{$search}%")
                    ->orWhereHas('supplier', function ($sq) use ($search) {
                        $sq->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by supplier
        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('purchase_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('purchase_date', '<=', $request->date_to);
        }

        $purchases = $query->paginate(15);

        // Statistics
        $todayPurchases = Purchase::whereDate('purchase_date', Carbon::today())
            ->sum('total_amount');

        $monthPurchases = Purchase::whereMonth('purchase_date', Carbon::now()->month)
            ->whereYear('purchase_date', Carbon::now()->year)
            ->sum('total_amount');

        $totalTransactions = Purchase::count();

        $averagePurchase = Purchase::avg('total_amount') ?? 0;

        // Get suppliers for filter
        $suppliers = Supplier::active()->orderBy('name')->get();

        return view('admin.purchases.index', compact(
            'purchases',
            'suppliers',
            'todayPurchases',
            'monthPurchases',
            'totalTransactions',
            'averagePurchase'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $suppliers = Supplier::active()->orderBy('name')->get();
        $products = Product::active()->with(['category', 'unit'])->orderBy('name')->get();

        return view('admin.purchases.create', compact('suppliers', 'products'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_date' => 'required|date',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $purchase = Purchase::create([
                'supplier_id' => $request->supplier_id,
                'user_id' => auth()->id(),
                'purchase_date' => $request->purchase_date,
                'notes' => $request->notes,
                'status' => 'pending',
            ]);

            $totalAmount = 0;

            foreach ($request->items as $item) {
                $totalPrice = $item['quantity'] * $item['unit_price'];

                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $totalPrice,
                ]);

                $totalAmount += $totalPrice;
            }

            $purchase->update([
                'total_amount' => $totalAmount,
                'remaining_amount' => $totalAmount,
            ]);

            DB::commit();

            return redirect()->route('admin.transactions.purchases.index')
                ->with('success', __('messages.created_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'An error occurred while creating the purchase.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Purchase $purchase): View
    {
        $purchase->load(['supplier', 'user', 'items.product.unit', 'items.product.category']);

        return view('admin.purchases.show', compact('purchase'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Purchase $purchase): View
    {
        $purchase->load(['items.product']);
        $suppliers = Supplier::active()->orderBy('name')->get();
        $products = Product::active()->with(['category', 'unit'])->orderBy('name')->get();

        return view('admin.purchases.edit', compact('purchase', 'suppliers', 'products'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Purchase $purchase): RedirectResponse
    {
        $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'purchase_date' => 'required|date',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $purchase->update([
                'supplier_id' => $request->supplier_id,
                'purchase_date' => $request->purchase_date,
                'notes' => $request->notes,
            ]);

            // Delete existing items
            $purchase->items()->delete();

            $totalAmount = 0;

            foreach ($request->items as $item) {
                $totalPrice = $item['quantity'] * $item['unit_price'];

                PurchaseItem::create([
                    'purchase_id' => $purchase->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $totalPrice,
                ]);

                $totalAmount += $totalPrice;
            }

            $purchase->update([
                'total_amount' => $totalAmount,
                'remaining_amount' => $totalAmount - $purchase->paid_amount,
            ]);

            DB::commit();

            return redirect()->route('admin.transactions.purchases.index')
                ->with('success', __('messages.updated_successfully'));
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'An error occurred while updating the purchase.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Purchase $purchase): RedirectResponse
    {
        try {
            $purchase->delete();

            return redirect()->route('admin.transactions.purchases.index')
                ->with('success', __('messages.deleted_successfully'));
        } catch (\Exception $e) {
            return back()->with('error', 'An error occurred while deleting the purchase.');
        }
    }

    /**
     * Complete a purchase
     */
    public function complete(Purchase $purchase): RedirectResponse
    {
        try {
            $purchase->complete();

            return back()->with('success', 'Purchase completed successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'An error occurred while completing the purchase.');
        }
    }

    /**
     * Add payment to a purchase
     */
    public function addPayment(Request $request, Purchase $purchase): RedirectResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:0.01|max:' . $purchase->remaining_amount,
        ]);

        try {
            $purchase->addPayment($request->amount);

            return back()->with('success', 'Payment added successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'An error occurred while adding payment.');
        }
    }
}
