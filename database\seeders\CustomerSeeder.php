<?php

namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-1001',
                'address' => '123 Main Street, Anytown, ST 12345',
                'account_balance' => 0,
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-1002',
                'address' => '456 Oak Avenue, Somewhere, ST 12346',
                'account_balance' => 0,
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-1003',
                'address' => '789 Pine Road, Elsewhere, ST 12347',
                'account_balance' => 0,
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-1004',
                'address' => '321 Elm Street, Nowhere, ST 12348',
                'account_balance' => 0,
                'is_active' => true,
            ],
            [
                'name' => 'Emma <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '******-1005',
                'address' => '654 Maple Lane, Anywhere, ST 12349',
                'account_balance' => 0,
                'is_active' => true,
            ],
            [
                'name' => 'Frank Miller',
                'email' => '<EMAIL>',
                'phone' => '******-1006',
                'address' => '987 Cedar Drive, Everywhere, ST 12350',
                'account_balance' => 0,
                'is_active' => true,
            ],
            [
                'name' => 'Grace Taylor',
                'email' => '<EMAIL>',
                'phone' => '******-1007',
                'address' => '147 Birch Court, Someplace, ST 12351',
                'account_balance' => 0,
                'is_active' => true,
            ],
            [
                'name' => 'Henry Anderson',
                'email' => '<EMAIL>',
                'phone' => '******-1008',
                'address' => '258 Spruce Way, Otherplace, ST 12352',
                'account_balance' => 0,
                'is_active' => true,
            ],
        ];

        foreach ($customers as $customerData) {
            Customer::create($customerData);
        }
    }
}
