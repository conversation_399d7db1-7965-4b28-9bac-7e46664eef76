body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    background-color: #000;
}

.webgl {
    position: fixed;
    top: 0;
    left: 0;
    outline: none;
}

nav {
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

nav a {
    text-decoration: none;
    color: white;
    font-weight: bold;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 1rem;
}

.title {
    color: white;
    position: absolute;
    top: 75%;
    left: 50%;
    transform: translate(-50%, -75%);
    font-size: 2rem;
}
