<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\UnitController;
use App\Http\Controllers\Admin\SupplierController;
use App\Http\Controllers\Admin\CustomerController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\PurchaseController;
use App\Http\Controllers\Admin\SaleController;
use App\Http\Controllers\Admin\PurchaseReturnController;
use App\Http\Controllers\Admin\SaleReturnController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group and admin prefix.
|
*/

Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

    // Master Data Management
    Route::prefix('master')->name('master.')->group(function () {
        Route::resource('categories', CategoryController::class);
        Route::resource('units', UnitController::class);
        Route::resource('suppliers', SupplierController::class);
        Route::resource('customers', CustomerController::class);
        Route::resource('products', ProductController::class);
    });

    // Transaction Management
    Route::prefix('transactions')->name('transactions.')->group(function () {
        Route::resource('purchases', PurchaseController::class);
        Route::resource('sales', SaleController::class);
        Route::resource('purchase-returns', PurchaseReturnController::class);
        Route::resource('sale-returns', SaleReturnController::class);
        
        // Additional transaction routes
        Route::post('purchases/{purchase}/complete', [PurchaseController::class, 'complete'])->name('purchases.complete');
        Route::post('sales/{sale}/complete', [SaleController::class, 'complete'])->name('sales.complete');
        Route::post('purchases/{purchase}/add-payment', [PurchaseController::class, 'addPayment'])->name('purchases.add-payment');
        Route::post('sales/{sale}/add-payment', [SaleController::class, 'addPayment'])->name('sales.add-payment');
    });

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('sales', [DashboardController::class, 'salesReport'])->name('sales');
        Route::get('purchases', [DashboardController::class, 'purchasesReport'])->name('purchases');
        Route::get('inventory', [DashboardController::class, 'inventoryReport'])->name('inventory');
        Route::get('customers', [DashboardController::class, 'customersReport'])->name('customers');
        Route::get('suppliers', [DashboardController::class, 'suppliersReport'])->name('suppliers');
    });

    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [DashboardController::class, 'settings'])->name('index');
        Route::post('/', [DashboardController::class, 'updateSettings'])->name('update');
    });

    // Quick Actions API endpoints
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('products/search', [ProductController::class, 'search'])->name('products.search');
        Route::get('customers/search', [CustomerController::class, 'search'])->name('customers.search');
        Route::get('suppliers/search', [SupplierController::class, 'search'])->name('suppliers.search');
        Route::get('dashboard/stats', [DashboardController::class, 'getStats'])->name('dashboard.stats');
    });
});
