<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from session, default to Arabic
        $locale = session('locale', 'ar');

        // Validate locale
        if (!in_array($locale, ['en', 'ar'])) {
            $locale = 'ar';
        }

        // Set application locale
        App::setLocale($locale);

        return $next($request);
    }
}
