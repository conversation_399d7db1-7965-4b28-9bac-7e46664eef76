<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Laravel')); ?> - <?php echo $__env->yieldContent('title', __('messages.dashboard')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="font-sans antialiased bg-gray-50 <?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <div class="min-h-screen flex">
        <!-- Modern Sidebar -->
        <div id="sidebar"
            class="fixed inset-y-0 <?php echo e(app()->getLocale() === 'ar' ? 'right-0' : 'left-0'); ?> z-50 w-72 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 transform -translate-x-full transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 shadow-2xl">
            <!-- Logo Section -->
            <div
                class="flex items-center justify-center h-20 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
                <div class="absolute inset-0 bg-black opacity-10"></div>
                <div
                    class="relative z-10 flex items-center space-x-3 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                    <div class="w-10 h-10 bg-white rounded-xl flex items-center justify-center shadow-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-white"><?php echo e(__('messages.pos_system')); ?></h1>
                        <p class="text-xs text-blue-100 opacity-75"><?php echo e(__('Admin Panel')); ?></p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="mt-6 px-4 space-y-2 flex-1 overflow-y-auto">
                <!-- Dashboard -->
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                    class="modern-nav-link <?php echo e(request()->routeIs('admin.dashboard*') ? 'active' : ''); ?>">
                    <div class="nav-icon-wrapper">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                            </path>
                        </svg>
                    </div>
                    <span class="nav-text"><?php echo e(__('messages.dashboard')); ?></span>
                    <div class="nav-indicator"></div>
                </a>

                <!-- Sales Section -->
                <div class="sidebar-section" x-data="{ open: <?php echo e(request()->routeIs('admin.transactions.sales*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="sidebar-section-header">
                        <div class="flex items-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                            <span><?php echo e(__('messages.sales')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition class="sidebar-submenu">
                        <a href="<?php echo e(route('admin.transactions.sales.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.transactions.sales.index') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.sales')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.sales.create')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.transactions.sales.create') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.new_sale')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.sale-returns.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.transactions.sale-returns*') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.returns')); ?>

                        </a>
                    </div>
                </div>

                <!-- Purchases Section -->
                <div class="sidebar-section" x-data="{ open: <?php echo e(request()->routeIs('admin.transactions.purchases*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="sidebar-section-header">
                        <div class="flex items-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            <span><?php echo e(__('messages.purchases')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition class="sidebar-submenu">
                        <a href="<?php echo e(route('admin.transactions.purchases.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.transactions.purchases.index') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.purchases')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.purchases.create')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.transactions.purchases.create') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.new_purchase')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.purchase-returns.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.transactions.purchase-returns*') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.returns')); ?>

                        </a>
                    </div>
                </div>

                <!-- Inventory Section -->
                <div class="sidebar-section" x-data="{ open: <?php echo e(request()->routeIs('admin.master.*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="sidebar-section-header">
                        <div class="flex items-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <span><?php echo e(__('messages.inventory')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition class="sidebar-submenu">
                        <a href="<?php echo e(route('admin.master.products.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.master.products*') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.products')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.master.categories.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.master.categories*') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.categories')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.master.units.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.master.units*') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.units')); ?>

                        </a>
                    </div>
                </div>

                <!-- Contacts Section -->
                <div class="sidebar-section" x-data="{ open: <?php echo e(request()->routeIs('admin.master.customers*') || request()->routeIs('admin.master.suppliers*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="sidebar-section-header">
                        <div class="flex items-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            <span><?php echo e(__('messages.contacts')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition class="sidebar-submenu">
                        <a href="<?php echo e(route('admin.master.customers.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.master.customers*') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.customers')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.master.suppliers.index')); ?>"
                            class="sidebar-sublink <?php echo e(request()->routeIs('admin.master.suppliers*') ? 'active' : ''); ?>">
                            <?php echo e(__('messages.suppliers')); ?>

                        </a>
                    </div>
                </div>

                <!-- Reports -->
                <a href="<?php echo e(route('admin.reports.sales')); ?>"
                    class="sidebar-link <?php echo e(request()->routeIs('admin.reports*') ? 'active' : ''); ?>">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    <span><?php echo e(__('messages.reports')); ?></span>
                </a>

                <!-- Settings -->
                <a href="<?php echo e(route('admin.settings.index')); ?>"
                    class="sidebar-link <?php echo e(request()->routeIs('admin.settings*') ? 'active' : ''); ?>">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span><?php echo e(__('messages.settings')); ?></span>
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:<?php echo e(app()->getLocale() === 'ar' ? 'mr-64' : 'ml-64'); ?>">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-4 py-3">
                    <!-- Mobile menu button -->
                    <button id="mobile-menu-button"
                        class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>

                    <!-- Page Title -->
                    <div class="flex-1">
                        <?php if (! empty(trim($__env->yieldContent('header')))): ?>
                            <?php echo $__env->yieldContent('header'); ?>
                        <?php else: ?>
                            <h1 class="text-xl font-semibold text-gray-900"><?php echo $__env->yieldContent('title', __('messages.dashboard')); ?></h1>
                        <?php endif; ?>
                    </div>

                    <!-- Right side -->
                    <div
                        class="flex items-center space-x-4 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                        <!-- Language Switcher -->
                        <div class="relative">
                            <select onchange="window.location.href=this.value"
                                class="bg-white border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="<?php echo e(route('language.switch', 'en')); ?>"
                                    <?php echo e(app()->getLocale() === 'en' ? 'selected' : ''); ?>>English</option>
                                <option value="<?php echo e(route('language.switch', 'ar')); ?>"
                                    <?php echo e(app()->getLocale() === 'ar' ? 'selected' : ''); ?>>العربية</option>
                            </select>
                        </div>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                    <span
                                        class="text-sm font-medium text-gray-700"><?php echo e(substr(Auth::user()->name, 0, 1)); ?></span>
                                </div>
                                <span class="ml-2 text-sm font-medium text-gray-700"><?php echo e(Auth::user()->name); ?></span>
                                <svg class="ml-1 h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition
                                class="absolute <?php echo e(app()->getLocale() === 'ar' ? 'left-0' : 'right-0'); ?> mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="<?php echo e(route('profile.edit')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"><?php echo e(__('messages.profile')); ?></a>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit"
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"><?php echo e(__('messages.logout')); ?></button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="p-6">
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 hidden lg:hidden"></div>

    <!-- Scripts -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebar-overlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH D:\projects\new_erd\resources\views/admin/layouts/app.blade.php ENDPATH**/ ?>