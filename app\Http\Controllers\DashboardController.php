<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Purchase;
use App\Models\Sale;
use App\Models\Customer;
use App\Models\Supplier;
use Illuminate\View\View;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(): View
    {
        // Get current month and year
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        // Sales statistics
        $totalSales = Sale::completed()->sum('total_amount');
        $monthlySales = Sale::completed()
            ->whereMonth('sale_date', $currentMonth)
            ->whereYear('sale_date', $currentYear)
            ->sum('total_amount');
        $todaySales = Sale::completed()
            ->whereDate('sale_date', Carbon::today())
            ->sum('total_amount');

        // Purchase statistics
        $totalPurchases = Purchase::completed()->sum('total_amount');
        $monthlyPurchases = Purchase::completed()
            ->whereMonth('purchase_date', $currentMonth)
            ->whereYear('purchase_date', $currentYear)
            ->sum('total_amount');

        // Product statistics
        $totalProducts = Product::active()->count();
        $lowStockProducts = Product::active()->lowStock()->count();
        $outOfStockProducts = Product::active()->where('stock_quantity', '<=', 0)->count();

        // Customer and Supplier statistics
        $totalCustomers = Customer::active()->count();
        $totalSuppliers = Supplier::active()->count();

        // Recent activities
        $recentSales = Sale::with(['customer', 'user'])
            ->latest()
            ->take(5)
            ->get();

        $recentPurchases = Purchase::with(['supplier', 'user'])
            ->latest()
            ->take(5)
            ->get();

        // Low stock products
        $lowStockProductsList = Product::with(['category', 'unit'])
            ->active()
            ->lowStock()
            ->orderBy('stock_quantity')
            ->take(10)
            ->get();

        // Monthly sales chart data (last 12 months)
        $monthlySalesData = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $sales = Sale::completed()
                ->whereMonth('sale_date', $date->month)
                ->whereYear('sale_date', $date->year)
                ->sum('total_amount');
            $monthlySalesData[] = [
                'month' => $date->format('M Y'),
                'sales' => $sales
            ];
        }

        return view('dashboard', compact(
            'totalSales',
            'monthlySales',
            'todaySales',
            'totalPurchases',
            'monthlyPurchases',
            'totalProducts',
            'lowStockProducts',
            'outOfStockProducts',
            'totalCustomers',
            'totalSuppliers',
            'recentSales',
            'recentPurchases',
            'lowStockProductsList',
            'monthlySalesData'
        ));
    }
}
