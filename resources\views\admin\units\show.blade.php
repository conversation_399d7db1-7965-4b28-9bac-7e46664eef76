@extends('admin.layouts.admin')

@section('title', __('messages.view') . ' ' . __('messages.unit'))

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">{{ __('messages.view') }} {{ __('messages.unit') }}</h1>
            <p class="text-gray-600 mt-1">{{ __('Unit details and information') }}</p>
        </div>
        <div class="flex space-x-3 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
            <a href="{{ route('admin.master.units.edit', $unit) }}" class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                {{ __('messages.edit') }}
            </a>
            <a href="{{ route('admin.master.units.index') }}" class="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                {{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Unit Information -->
    <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
        <div class="p-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Information -->
                <div class="space-y-6">
                    <h2 class="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-2">{{ __('Basic Information') }}</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-semibold text-gray-600 mb-1">{{ __('messages.unit_name') }}</label>
                            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                                <p class="text-lg font-medium text-gray-900">{{ $unit->name }}</p>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-semibold text-gray-600 mb-1">{{ __('Unit Symbol') }}</label>
                            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                                <p class="text-lg font-medium text-gray-900">{{ $unit->symbol }}</p>
                            </div>
                        </div>

                        @if($unit->description)
                        <div>
                            <label class="block text-sm font-semibold text-gray-600 mb-1">{{ __('messages.description') }}</label>
                            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                                <p class="text-gray-900">{{ $unit->description }}</p>
                            </div>
                        </div>
                        @endif

                        <div>
                            <label class="block text-sm font-semibold text-gray-600 mb-1">{{ __('messages.status') }}</label>
                            <div class="bg-gray-50 rounded-xl p-4 border border-gray-200">
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full {{ $unit->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $unit->is_active ? __('messages.active') : __('messages.inactive') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="space-y-6">
                    <h2 class="text-xl font-semibold text-gray-900 border-b border-gray-200 pb-2">{{ __('Usage Statistics') }}</h2>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-blue-100 text-sm font-medium">{{ __('Products Using This Unit') }}</p>
                                    <p class="text-2xl font-bold">{{ $unit->products_count ?? 0 }}</p>
                                </div>
                                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-green-100 text-sm font-medium">{{ __('Total Stock Value') }}</p>
                                    <p class="text-2xl font-bold">${{ number_format($unit->total_stock_value ?? 0, 2) }}</p>
                                </div>
                                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Products -->
                    @if(isset($unit->products) && $unit->products->count() > 0)
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('Recent Products') }}</h3>
                        <div class="space-y-2">
                            @foreach($unit->products->take(5) as $product)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl border border-gray-200">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $product->name }}</p>
                                    <p class="text-sm text-gray-500">{{ $product->sku }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">{{ $product->stock_quantity }} {{ $unit->symbol }}</p>
                                    <p class="text-xs text-gray-500">${{ number_format($product->selling_price, 2) }}</p>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        @if($unit->products->count() > 5)
                        <div class="mt-3 text-center">
                            <a href="{{ route('admin.master.products.index', ['unit_id' => $unit->id]) }}" class="text-orange-600 hover:text-orange-800 font-medium">
                                {{ __('View all products') }} ({{ $unit->products->count() - 5 }} {{ __('more') }})
                            </a>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ __('System Information') }}
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-semibold text-gray-600">{{ __('Created') }}:</span>
                <span class="text-gray-800">{{ $unit->created_at->format('M d, Y H:i') }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-600">{{ __('Last Updated') }}:</span>
                <span class="text-gray-800">{{ $unit->updated_at->format('M d, Y H:i') }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-600">{{ __('Unit ID') }}:</span>
                <span class="text-gray-800">#{{ $unit->id }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-600">{{ __('Usage Count') }}:</span>
                <span class="text-gray-800">{{ $unit->products_count ?? 0 }} {{ __('products') }}</span>
            </div>
        </div>
    </div>
</div>
@endsection
