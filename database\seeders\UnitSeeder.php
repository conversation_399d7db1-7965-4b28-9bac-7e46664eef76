<?php

namespace Database\Seeders;

use App\Models\Unit;
use Illuminate\Database\Seeder;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = [
            [
                'name' => 'Piece',
                'symbol' => 'pcs',
                'description' => 'Individual items or pieces',
                'is_active' => true,
            ],
            [
                'name' => 'Kilogram',
                'symbol' => 'kg',
                'description' => 'Weight measurement in kilograms',
                'is_active' => true,
            ],
            [
                'name' => 'Gram',
                'symbol' => 'g',
                'description' => 'Weight measurement in grams',
                'is_active' => true,
            ],
            [
                'name' => 'Liter',
                'symbol' => 'L',
                'description' => 'Volume measurement in liters',
                'is_active' => true,
            ],
            [
                'name' => 'Milliliter',
                'symbol' => 'mL',
                'description' => 'Volume measurement in milliliters',
                'is_active' => true,
            ],
            [
                'name' => 'Meter',
                'symbol' => 'm',
                'description' => 'Length measurement in meters',
                'is_active' => true,
            ],
            [
                'name' => 'Centimeter',
                'symbol' => 'cm',
                'description' => 'Length measurement in centimeters',
                'is_active' => true,
            ],
            [
                'name' => 'Box',
                'symbol' => 'box',
                'description' => 'Items sold in boxes',
                'is_active' => true,
            ],
            [
                'name' => 'Pack',
                'symbol' => 'pack',
                'description' => 'Items sold in packs',
                'is_active' => true,
            ],
            [
                'name' => 'Dozen',
                'symbol' => 'dz',
                'description' => 'Items sold by the dozen (12 pieces)',
                'is_active' => true,
            ],
            [
                'name' => 'Pair',
                'symbol' => 'pair',
                'description' => 'Items sold in pairs',
                'is_active' => true,
            ],
            [
                'name' => 'Set',
                'symbol' => 'set',
                'description' => 'Items sold as a set',
                'is_active' => true,
            ],
        ];

        foreach ($units as $unitData) {
            Unit::create($unitData);
        }
    }
}
