<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Category;
use App\Models\Unit;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get categories and units
        $smartphones = Category::where('name', 'Smartphones')->first();
        $laptops = Category::where('name', 'Laptops')->first();
        $accessories = Category::where('name', 'Accessories')->first();
        $menClothing = Category::where('name', 'Men\'s Clothing')->first();
        $womenClothing = Category::where('name', 'Women\'s Clothing')->first();
        $snacks = Category::where('name', 'Snacks')->first();
        $beverages = Category::where('name', 'Beverages')->first();
        $freshProduce = Category::where('name', 'Fresh Produce')->first();

        $piece = Unit::where('symbol', 'pcs')->first();
        $kg = Unit::where('symbol', 'kg')->first();
        $liter = Unit::where('symbol', 'L')->first();
        $pack = Unit::where('symbol', 'pack')->first();
        $pair = Unit::where('symbol', 'pair')->first();

        $products = [
            // Electronics
            [
                'name' => 'iPhone 15 Pro',
                'sku' => 'IPH15PRO001',
                'description' => 'Latest iPhone with advanced camera system',
                'category_id' => $smartphones->id,
                'unit_id' => $piece->id,
                'purchase_price' => 800.00,
                'sale_price' => 999.00,
                'stock_quantity' => 25,
                'minimum_stock' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Samsung Galaxy S24',
                'sku' => 'SAM24001',
                'description' => 'Premium Android smartphone',
                'category_id' => $smartphones->id,
                'unit_id' => $piece->id,
                'purchase_price' => 700.00,
                'sale_price' => 899.00,
                'stock_quantity' => 30,
                'minimum_stock' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'MacBook Pro 16"',
                'sku' => 'MBP16001',
                'description' => 'Professional laptop for creative work',
                'category_id' => $laptops->id,
                'unit_id' => $piece->id,
                'purchase_price' => 2000.00,
                'sale_price' => 2499.00,
                'stock_quantity' => 15,
                'minimum_stock' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Dell XPS 13',
                'sku' => 'DELLXPS001',
                'description' => 'Ultrabook for business professionals',
                'category_id' => $laptops->id,
                'unit_id' => $piece->id,
                'purchase_price' => 900.00,
                'sale_price' => 1199.00,
                'stock_quantity' => 20,
                'minimum_stock' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Wireless Earbuds',
                'sku' => 'WEARB001',
                'description' => 'Bluetooth wireless earbuds with noise cancellation',
                'category_id' => $accessories->id,
                'unit_id' => $pair->id,
                'purchase_price' => 80.00,
                'sale_price' => 129.00,
                'stock_quantity' => 50,
                'minimum_stock' => 10,
                'is_active' => true,
            ],
            // Clothing
            [
                'name' => 'Men\'s Cotton T-Shirt',
                'sku' => 'MTSHIRT001',
                'description' => 'Comfortable cotton t-shirt for men',
                'category_id' => $menClothing->id,
                'unit_id' => $piece->id,
                'purchase_price' => 8.00,
                'sale_price' => 19.99,
                'stock_quantity' => 100,
                'minimum_stock' => 20,
                'is_active' => true,
            ],
            [
                'name' => 'Women\'s Jeans',
                'sku' => 'WJEANS001',
                'description' => 'Stylish denim jeans for women',
                'category_id' => $womenClothing->id,
                'unit_id' => $piece->id,
                'purchase_price' => 25.00,
                'sale_price' => 59.99,
                'stock_quantity' => 75,
                'minimum_stock' => 15,
                'is_active' => true,
            ],
            // Food & Beverages
            [
                'name' => 'Potato Chips',
                'sku' => 'CHIPS001',
                'description' => 'Crispy potato chips - family size',
                'category_id' => $snacks->id,
                'unit_id' => $pack->id,
                'purchase_price' => 1.50,
                'sale_price' => 3.99,
                'stock_quantity' => 200,
                'minimum_stock' => 50,
                'is_active' => true,
            ],
            [
                'name' => 'Orange Juice',
                'sku' => 'OJ001',
                'description' => 'Fresh orange juice - 1 liter',
                'category_id' => $beverages->id,
                'unit_id' => $liter->id,
                'purchase_price' => 2.00,
                'sale_price' => 4.99,
                'stock_quantity' => 80,
                'minimum_stock' => 20,
                'is_active' => true,
            ],
            [
                'name' => 'Bananas',
                'sku' => 'BANANA001',
                'description' => 'Fresh bananas',
                'category_id' => $freshProduce->id,
                'unit_id' => $kg->id,
                'purchase_price' => 1.00,
                'sale_price' => 2.49,
                'stock_quantity' => 50,
                'minimum_stock' => 10,
                'is_active' => true,
            ],
        ];

        foreach ($products as $productData) {
            Product::create($productData);
        }
    }
}
