<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Laravel')); ?> - <?php echo $__env->yieldContent('title', __('messages.dashboard')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Arabic Font - Tajawal -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
        rel="stylesheet">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body
    class="<?php echo e(app()->getLocale() === 'ar' ? 'font-arabic text-arabic' : 'font-inter'); ?> antialiased bg-gradient-to-br from-slate-50 to-blue-50 <?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <div class="min-h-screen flex">
        <!-- Modern Sidebar -->
        <div id="sidebar"
            class="fixed inset-y-0 <?php echo e(app()->getLocale() === 'ar' ? 'right-0' : 'left-0'); ?> z-50 w-72 bg-white transform -translate-x-full transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 shadow-xl border-r border-gray-200">
            <!-- Logo Section -->
            <div class="relative h-20 bg-gradient-to-r from-blue-600 to-blue-700 overflow-hidden">
                <div class="absolute inset-0 bg-black opacity-10"></div>
                <div class="relative z-10 flex items-center justify-center h-full px-6">
                    <div class="flex items-center space-x-3 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                        <div
                            class="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
                            <svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-white"><?php echo e(__('messages.pos_system')); ?></h1>
                            <p class="text-xs text-blue-100 opacity-75"><?php echo e(__('Admin Panel')); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                <!-- Dashboard -->
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                    class="modern-nav-link <?php echo e(request()->routeIs('admin.dashboard*') ? 'active' : ''); ?>">
                    <div class="nav-icon-wrapper">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                            </path>
                        </svg>
                    </div>
                    <span class="nav-text"><?php echo e(__('messages.dashboard')); ?></span>
                    <div class="nav-indicator"></div>
                </a>

                <!-- Sales Section -->
                <div class="modern-nav-section" x-data="{ open: <?php echo e(request()->routeIs('admin.transactions.sales*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="modern-section-header w-full">
                        <div class="flex items-center">
                            <div class="nav-icon-wrapper bg-green-500">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                            </div>
                            <span class="nav-text"><?php echo e(__('messages.sales')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-300 text-gray-400"
                            :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform -translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0" class="modern-submenu">
                        <a href="<?php echo e(route('admin.transactions.sales.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.transactions.sales.index') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                                    </path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.sales')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.sales.create')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.transactions.sales.create') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.new_sale')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.sale-returns.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.transactions.sale-returns*') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.returns')); ?>

                        </a>
                    </div>
                </div>

                <!-- Purchases Section -->
                <div class="modern-nav-section" x-data="{ open: <?php echo e(request()->routeIs('admin.transactions.purchases*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="modern-section-header w-full">
                        <div class="flex items-center">
                            <div class="nav-icon-wrapper bg-orange-500">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                            </div>
                            <span class="nav-text"><?php echo e(__('messages.purchases')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-300 text-gray-400"
                            :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform -translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0" class="modern-submenu">
                        <a href="<?php echo e(route('admin.transactions.purchases.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.transactions.purchases.index') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                                    </path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.purchases')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.purchases.create')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.transactions.purchases.create') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.new_purchase')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.purchase-returns.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.transactions.purchase-returns*') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.returns')); ?>

                        </a>
                    </div>
                </div>

                <!-- Inventory Section -->
                <div class="modern-nav-section" x-data="{ open: <?php echo e(request()->routeIs('admin.master.*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="modern-section-header w-full">
                        <div class="flex items-center">
                            <div class="nav-icon-wrapper bg-purple-500">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <span class="nav-text"><?php echo e(__('messages.inventory')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-300 text-gray-400"
                            :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform -translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0" class="modern-submenu">
                        <a href="<?php echo e(route('admin.master.products.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.master.products*') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.products')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.master.categories.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.master.categories*') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                                    </path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.categories')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.master.units.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.master.units*') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                                    </path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.units')); ?>

                        </a>
                    </div>
                </div>

                <!-- Contacts Section -->
                <div class="modern-nav-section" x-data="{ open: <?php echo e(request()->routeIs('admin.master.customers*') || request()->routeIs('admin.master.suppliers*') ? 'true' : 'false'); ?> }">
                    <button @click="open = !open" class="modern-section-header w-full">
                        <div class="flex items-center">
                            <div class="nav-icon-wrapper bg-cyan-500">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                    </path>
                                </svg>
                            </div>
                            <span class="nav-text"><?php echo e(__('messages.contacts')); ?></span>
                        </div>
                        <svg class="w-4 h-4 transition-transform duration-300 text-gray-400"
                            :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                            </path>
                        </svg>
                    </button>
                    <div x-show="open" x-transition:enter="transition ease-out duration-200"
                        x-transition:enter-start="opacity-0 transform -translate-y-2"
                        x-transition:enter-end="opacity-100 transform translate-y-0" class="modern-submenu">
                        <a href="<?php echo e(route('admin.master.customers.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.master.customers*') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.customers')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.master.suppliers.index')); ?>"
                            class="modern-sublink <?php echo e(request()->routeIs('admin.master.suppliers*') ? 'active' : ''); ?>">
                            <div class="sublink-icon">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                            </div>
                            <?php echo e(__('messages.suppliers')); ?>

                        </a>
                    </div>
                </div>

                <!-- Reports -->
                <a href="<?php echo e(route('admin.reports.sales')); ?>"
                    class="modern-nav-link <?php echo e(request()->routeIs('admin.reports*') ? 'active' : ''); ?>">
                    <div class="nav-icon-wrapper bg-indigo-500">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                    <span class="nav-text"><?php echo e(__('messages.reports')); ?></span>
                    <div class="nav-indicator"></div>
                </a>

                <!-- Settings -->
                <a href="<?php echo e(route('admin.settings.index')); ?>"
                    class="modern-nav-link <?php echo e(request()->routeIs('admin.settings*') ? 'active' : ''); ?>">
                    <div class="nav-icon-wrapper bg-gray-500">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <span class="nav-text"><?php echo e(__('messages.settings')); ?></span>
                    <div class="nav-indicator"></div>
                </a>
            </nav>

            <!-- User Profile Section -->
            <div class="p-4 border-t border-gray-200">
                <div
                    class="flex items-center space-x-3 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?> p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-all duration-300">
                    <div
                        class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold">
                        <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate"><?php echo e(Auth::user()->name); ?></p>
                        <p class="text-xs text-gray-500 truncate"><?php echo e(Auth::user()->email); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 lg:<?php echo e(app()->getLocale() === 'ar' ? 'mr-72' : 'ml-72'); ?>">
            <!-- Modern Header -->
            <header class="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 shadow-sm sticky top-0 z-40">
                <div class="flex items-center justify-between px-6 py-4">
                    <!-- Mobile menu button -->
                    <button id="mobile-menu-button"
                        class="lg:hidden p-2 rounded-xl text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-all duration-200">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>

                    <!-- Page Title -->
                    <div class="flex-1">
                        <?php if (! empty(trim($__env->yieldContent('header')))): ?>
                            <?php echo $__env->yieldContent('header'); ?>
                        <?php else: ?>
                            <div
                                class="flex items-center space-x-3 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                                <h1
                                    class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                                    <?php echo $__env->yieldContent('title', __('messages.dashboard')); ?></h1>
                                <?php if (! empty(trim($__env->yieldContent('breadcrumb')))): ?>
                                    <nav class="flex" aria-label="Breadcrumb">
                                        <ol
                                            class="flex items-center space-x-2 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                                            <?php echo $__env->yieldContent('breadcrumb'); ?>
                                        </ol>
                                    </nav>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Right side -->
                    <div
                        class="flex items-center space-x-4 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?>">
                        <!-- Language Switcher -->
                        <div class="relative">
                            <select onchange="window.location.href=this.value"
                                class="bg-white/50 backdrop-blur border border-gray-300 rounded-xl px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                                <option value="<?php echo e(route('language.switch', 'en')); ?>"
                                    <?php echo e(app()->getLocale() === 'en' ? 'selected' : ''); ?>>English</option>
                                <option value="<?php echo e(route('language.switch', 'ar')); ?>"
                                    <?php echo e(app()->getLocale() === 'ar' ? 'selected' : ''); ?>>العربية</option>
                            </select>
                        </div>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                class="flex items-center space-x-2 <?php echo e(app()->getLocale() === 'ar' ? 'space-x-reverse' : ''); ?> text-sm rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 p-2 hover:bg-gray-100 transition-all duration-200">
                                <div
                                    class="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                                    <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                </div>
                                <span
                                    class="font-medium text-gray-700 hidden md:block"><?php echo e(Auth::user()->name); ?></span>
                                <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition
                                class="absolute <?php echo e(app()->getLocale() === 'ar' ? 'left-0' : 'right-0'); ?> mt-2 w-48 bg-white rounded-xl shadow-lg py-1 z-50 border border-gray-200">
                                <a href="<?php echo e(route('profile.edit')); ?>"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg mx-1"><?php echo e(__('messages.profile')); ?></a>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit"
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg mx-1"><?php echo e(__('messages.logout')); ?></button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="p-6">
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 hidden lg:hidden"></div>

    <!-- Scripts -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebar-overlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH D:\projects\new_erd\resources\views/admin/layouts/admin.blade.php ENDPATH**/ ?>