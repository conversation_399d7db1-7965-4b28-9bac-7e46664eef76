<?php $__env->startSection('title', __('messages.dashboard')); ?>

<?php $__env->startSection('content'); ?>
    <div class="space-y-6">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Today's Sales -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                        </div>
                        <div class="<?php echo e(app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5'); ?> w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-blue-100 truncate"><?php echo e(__('messages.todays_sales')); ?>

                                </dt>
                                <dd class="text-lg font-medium text-white">$<?php echo e(number_format($todaySales, 2)); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Sales -->
            <div class="bg-gradient-to-r from-green-500 to-green-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                        </div>
                        <div class="<?php echo e(app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5'); ?> w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-green-100 truncate"><?php echo e(__('messages.monthly_sales')); ?>

                                </dt>
                                <dd class="text-lg font-medium text-white">$<?php echo e(number_format($monthlySales, 2)); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Products -->
            <div class="bg-gradient-to-r from-purple-500 to-purple-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="<?php echo e(app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5'); ?> w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-purple-100 truncate"><?php echo e(__('messages.total_products')); ?>

                                </dt>
                                <dd class="text-lg font-medium text-white"><?php echo e(number_format($totalProducts)); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Low Stock Alert -->
            <div class="bg-gradient-to-r from-red-500 to-red-600 overflow-hidden shadow-lg rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                </path>
                            </svg>
                        </div>
                        <div class="<?php echo e(app()->getLocale() === 'ar' ? 'mr-5' : 'ml-5'); ?> w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-red-100 truncate"><?php echo e(__('messages.low_stock_items')); ?>

                                </dt>
                                <dd class="text-lg font-medium text-white"><?php echo e(number_format($lowStockProducts)); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions and Recent Activities -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Quick Actions -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?php echo e(__('messages.quick_actions')); ?></h3>
                    <div class="space-y-3">
                        <a href="<?php echo e(route('admin.transactions.sales.create')); ?>"
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 <?php echo e(app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2'); ?>" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <?php echo e(__('messages.new_sale')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.transactions.purchases.create')); ?>"
                            class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 <?php echo e(app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2'); ?>" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            <?php echo e(__('messages.new_purchase')); ?>

                        </a>
                        <a href="<?php echo e(route('admin.master.products.create')); ?>"
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 <?php echo e(app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2'); ?>" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <?php echo e(__('messages.add_product')); ?>

                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Sales -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?php echo e(__('messages.recent_sales')); ?></h3>
                    <div class="space-y-3">
                        <?php $__empty_1 = true; $__currentLoopData = $recentSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                <div>
                                    <p class="text-sm font-medium text-gray-900"><?php echo e($sale->sale_number); ?></p>
                                    <p class="text-xs text-gray-500">
                                        <?php echo e($sale->customer->name ?? __('messages.walk_in_customer')); ?></p>
                                </div>
                                <div class="text-<?php echo e(app()->getLocale() === 'ar' ? 'left' : 'right'); ?>">
                                    <p class="text-sm font-medium text-gray-900">
                                        $<?php echo e(number_format($sale->total_amount, 2)); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e($sale->sale_date->format('M d')); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <p class="text-gray-500 text-sm"><?php echo e(__('messages.no_recent_sales')); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="mt-4">
                        <a href="<?php echo e(route('admin.transactions.sales.index')); ?>"
                            class="text-blue-600 hover:text-blue-800 text-sm font-medium"><?php echo e(__('messages.view_all_sales')); ?>

                            →</a>
                    </div>
                </div>
            </div>

            <!-- Low Stock Products -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4"><?php echo e(__('messages.low_stock_alert')); ?></h3>
                    <div class="space-y-3">
                        <?php $__empty_1 = true; $__currentLoopData = $lowStockProductsList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="flex justify-between items-center py-2 border-b border-gray-200">
                                <div>
                                    <p class="text-sm font-medium text-gray-900"><?php echo e($product->name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e($product->category->name); ?></p>
                                </div>
                                <div class="text-<?php echo e(app()->getLocale() === 'ar' ? 'left' : 'right'); ?>">
                                    <p class="text-sm font-medium text-red-600"><?php echo e($product->stock_quantity); ?>

                                        <?php echo e($product->unit->symbol); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e(__('Min')); ?>: <?php echo e($product->minimum_stock); ?>

                                    </p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <p class="text-gray-500 text-sm"><?php echo e(__('messages.all_products_well_stocked')); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="mt-4">
                        <a href="<?php echo e(route('admin.master.products.index')); ?>?stock_status=low_stock"
                            class="text-red-600 hover:text-red-800 text-sm font-medium"><?php echo e(__('messages.view_all_low_stock')); ?>

                            →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\new_erd\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>