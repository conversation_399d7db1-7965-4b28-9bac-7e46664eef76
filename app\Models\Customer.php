<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'account_balance',
        'is_active',
    ];

    protected $casts = [
        'account_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function saleReturns(): HasMany
    {
        return $this->hasMany(SaleReturn::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Business Logic Methods
    public function updateBalance(float $amount): void
    {
        $this->increment('account_balance', $amount);
    }

    public function getTotalSalesAttribute(): float
    {
        return $this->sales()->sum('total_amount');
    }

    public function getTotalPaidAttribute(): float
    {
        return $this->sales()->sum('paid_amount');
    }

    public function getTotalDueAttribute(): float
    {
        return $this->sales()->sum('remaining_amount');
    }
}
