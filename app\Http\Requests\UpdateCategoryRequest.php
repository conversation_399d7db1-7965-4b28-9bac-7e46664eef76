<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $category = $this->route('category');
        $categoryId = is_object($category) ? $category->id : $category;

        return [
            'name' => 'required|string|max:255|unique:categories,name,' . $categoryId,
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:categories,id|not_in:' . $categoryId,
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => __('Category name is required.'),
            'name.unique' => __('Category name already exists.'),
            'name.max' => __('Category name cannot exceed 255 characters.'),
            'description.max' => __('Description cannot exceed 1000 characters.'),
            'parent_id.exists' => __('Selected parent category does not exist.'),
            'parent_id.not_in' => __('Category cannot be its own parent.'),
        ];
    }
}
