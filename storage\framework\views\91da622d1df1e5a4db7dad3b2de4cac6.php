<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo e(__('messages.pos_system')); ?> - <?php echo e(__('messages.modern_pos_solution')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Arabic Font - Noto Kufi Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body
    class="<?php echo e(app()->getLocale() === 'ar' ? 'font-arabic text-arabic' : 'font-inter'); ?> antialiased bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <!-- Navigation -->
    <nav class="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1
                            class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            <?php echo e(__('messages.pos_system')); ?>

                        </h1>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative">
                        <select onchange="window.location.href=this.value"
                            class="bg-white border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="<?php echo e(route('language.switch', 'en')); ?>"
                                <?php echo e(app()->getLocale() === 'en' ? 'selected' : ''); ?>>English</option>
                            <option value="<?php echo e(route('language.switch', 'ar')); ?>"
                                <?php echo e(app()->getLocale() === 'ar' ? 'selected' : ''); ?>>العربية</option>
                        </select>
                    </div>

                    <?php if(Route::has('login')): ?>
                        <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('admin.dashboard')); ?>"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                                <?php echo e(__('Dashboard')); ?>

                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('login')); ?>"
                                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                                <?php echo e(__('Log in')); ?>

                            </a>
                            <?php if(Route::has('register')): ?>
                                <a href="<?php echo e(route('register')); ?>"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                                    <?php echo e(__('Register')); ?>

                                </a>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                    <?php echo e(__('Modern Point of Sale')); ?>

                    <span class="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <?php echo e(__('System')); ?>

                    </span>
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                    <?php echo e(__('Streamline your business operations with our comprehensive POS solution. Manage inventory, track sales, handle customers, and grow your business efficiently.')); ?>

                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('admin.dashboard')); ?>"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition duration-200 transform hover:scale-105">
                            <?php echo e(__('Go to Dashboard')); ?>

                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('register')); ?>"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold transition duration-200 transform hover:scale-105">
                            <?php echo e(__('Get Started')); ?>

                        </a>
                        <a href="<?php echo e(route('login')); ?>"
                            class="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3 rounded-lg text-lg font-semibold transition duration-200">
                            <?php echo e(__('Sign In')); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Background decoration -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
            <div
                class="absolute top-1/4 left-1/4 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob">
            </div>
            <div
                class="absolute top-1/3 right-1/4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000">
            </div>
            <div
                class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000">
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    <?php echo e(__('Powerful Features')); ?>

                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    <?php echo e(__('Everything you need to manage your business efficiently')); ?>

                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl">
                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('Sales Management')); ?></h3>
                    <p class="text-gray-600">
                        <?php echo e(__('Process sales quickly with an intuitive interface. Track payments and manage customer transactions.')); ?>

                    </p>
                </div>

                <!-- Feature 2 -->
                <div class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl">
                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('Inventory Control')); ?></h3>
                    <p class="text-gray-600">
                        <?php echo e(__('Keep track of your products, stock levels, and get alerts when inventory runs low.')); ?>

                    </p>
                </div>

                <!-- Feature 3 -->
                <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl">
                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('Customer Management')); ?></h3>
                    <p class="text-gray-600">
                        <?php echo e(__('Maintain customer records, track purchase history, and manage account balances.')); ?></p>
                </div>

                <!-- Feature 4 -->
                <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 p-6 rounded-xl">
                    <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('Purchase Orders')); ?></h3>
                    <p class="text-gray-600">
                        <?php echo e(__('Manage supplier relationships and track purchase orders with automatic stock updates.')); ?>

                    </p>
                </div>

                <!-- Feature 5 -->
                <div class="bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-xl">
                    <div class="w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('Reports & Analytics')); ?></h3>
                    <p class="text-gray-600">
                        <?php echo e(__('Generate detailed reports on sales, inventory, and business performance.')); ?></p>
                </div>

                <!-- Feature 6 -->
                <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-xl">
                    <div class="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('Multi-language')); ?></h3>
                    <p class="text-gray-600">
                        <?php echo e(__('Full support for Arabic and English with RTL layout for Arabic users.')); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-4"><?php echo e(__('POS System')); ?></h3>
                <p class="text-gray-400 mb-8"><?php echo e(__('Modern Point of Sale Solution for Your Business')); ?></p>
                <div class="flex justify-center space-x-6">
                    <?php if(Route::has('login')): ?>
                        <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('admin.dashboard')); ?>"
                                class="text-gray-400 hover:text-white transition duration-200">
                                <?php echo e(__('Dashboard')); ?>

                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('login')); ?>"
                                class="text-gray-400 hover:text-white transition duration-200">
                                <?php echo e(__('Login')); ?>

                            </a>
                            <a href="<?php echo e(route('register')); ?>"
                                class="text-gray-400 hover:text-white transition duration-200">
                                <?php echo e(__('Register')); ?>

                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="mt-8 pt-8 border-t border-gray-800 text-gray-400 text-sm">
                    <p>&copy; <?php echo e(date('Y')); ?> <?php echo e(__('POS System. All rights reserved.')); ?></p>
                </div>
            </div>
        </div>
    </footer>


</body>

</html>
<?php /**PATH D:\projects\new_erd\resources\views/landing.blade.php ENDPATH**/ ?>