@extends('admin.layouts.admin')

@section('title', __('messages.purchase_details'))

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">{{ __('messages.purchase_details') }}</h1>
            <p class="text-gray-600 mt-1">{{ __('Purchase Order') }} #{{ $purchase->purchase_number }}</p>
        </div>
        <div class="flex space-x-3 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
            @if($purchase->status === 'pending')
                <a href="{{ route('admin.transactions.purchases.edit', $purchase) }}" class="bg-emerald-500 hover:bg-emerald-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    {{ __('messages.edit') }}
                </a>
            @endif
            <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                {{ __('Print') }}
            </button>
            <a href="{{ route('admin.transactions.purchases.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                {{ __('messages.back') }}
            </a>
        </div>
    </div>

    <!-- Purchase Status -->
    <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">{{ __('Purchase Status') }}</h3>
                        <p class="text-sm text-gray-500">{{ __('Current status of this purchase order') }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
                    @if($purchase->status === 'completed')
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Completed') }}
                        </span>
                    @elseif($purchase->status === 'pending')
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Pending') }}
                        </span>
                        <form action="{{ route('admin.transactions.purchases.complete', $purchase) }}" method="POST" class="inline-block">
                            @csrf
                            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200" 
                                    onclick="return confirm('{{ __('Are you sure you want to complete this purchase?') }}')">
                                {{ __('Complete Purchase') }}
                            </button>
                        </form>
                    @else
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'ml-2' : 'mr-2' }}" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Cancelled') }}
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Purchase Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Purchase Details -->
        <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
            <div class="bg-gradient-to-r from-indigo-500 to-blue-600 px-6 py-4">
                <h3 class="text-lg font-bold text-white">{{ __('Purchase Information') }}</h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-semibold text-gray-500">{{ __('Purchase Number') }}</label>
                        <p class="text-lg font-bold text-gray-900">{{ $purchase->purchase_number }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold text-gray-500">{{ __('Purchase Date') }}</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $purchase->purchase_date->format('M d, Y') }}</p>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-500">{{ __('Created By') }}</label>
                    <p class="text-lg font-semibold text-gray-900">{{ $purchase->user->name ?? 'N/A' }}</p>
                </div>
                <div>
                    <label class="block text-sm font-semibold text-gray-500">{{ __('Created At') }}</label>
                    <p class="text-lg font-semibold text-gray-900">{{ $purchase->created_at->format('M d, Y H:i') }}</p>
                </div>
                @if($purchase->notes)
                    <div>
                        <label class="block text-sm font-semibold text-gray-500">{{ __('messages.notes') }}</label>
                        <p class="text-gray-900 bg-gray-50 p-3 rounded-lg">{{ $purchase->notes }}</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Supplier Information -->
        <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
            <div class="bg-gradient-to-r from-emerald-500 to-teal-600 px-6 py-4">
                <h3 class="text-lg font-bold text-white">{{ __('Supplier Information') }}</h3>
            </div>
            <div class="p-6 space-y-4">
                <div>
                    <label class="block text-sm font-semibold text-gray-500">{{ __('messages.supplier') }}</label>
                    <p class="text-xl font-bold text-gray-900">{{ $purchase->supplier->name ?? 'N/A' }}</p>
                </div>
                @if($purchase->supplier->phone)
                    <div>
                        <label class="block text-sm font-semibold text-gray-500">{{ __('messages.phone') }}</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $purchase->supplier->phone }}</p>
                    </div>
                @endif
                @if($purchase->supplier->email)
                    <div>
                        <label class="block text-sm font-semibold text-gray-500">{{ __('messages.email') }}</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $purchase->supplier->email }}</p>
                    </div>
                @endif
                @if($purchase->supplier->address)
                    <div>
                        <label class="block text-sm font-semibold text-gray-500">{{ __('messages.address') }}</label>
                        <p class="text-gray-900 bg-gray-50 p-3 rounded-lg">{{ $purchase->supplier->address }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Purchase Items -->
    <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
        <div class="bg-gradient-to-r from-amber-500 to-orange-600 px-6 py-4">
            <h3 class="text-lg font-bold text-white">{{ __('Purchase Items') }}</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-bold text-gray-600 uppercase tracking-wider">
                            {{ __('messages.product') }}
                        </th>
                        <th class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-bold text-gray-600 uppercase tracking-wider">
                            {{ __('messages.quantity') }}
                        </th>
                        <th class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-bold text-gray-600 uppercase tracking-wider">
                            {{ __('Unit Price') }}
                        </th>
                        <th class="px-6 py-3 text-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }} text-xs font-bold text-gray-600 uppercase tracking-wider">
                            {{ __('messages.total') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($purchase->items ?? [] as $item)
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-bold text-gray-900">{{ $item->product->name ?? 'N/A' }}</div>
                                        <div class="text-xs text-gray-500">
                                            {{ $item->product->category->name ?? '' }} - {{ $item->product->unit->symbol ?? '' }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-semibold text-gray-900">{{ number_format($item->quantity, 2) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-semibold text-gray-900">${{ number_format($item->unit_price, 2) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-gray-900">${{ number_format($item->total_price, 2) }}</div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                {{ __('No items found') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Purchase Summary -->
        <div class="bg-gray-50 px-6 py-4">
            <div class="flex justify-end">
                <div class="w-64 space-y-2">
                    <div class="flex justify-between items-center py-2 border-b border-gray-200">
                        <span class="text-lg font-semibold text-gray-700">{{ __('Subtotal') }}:</span>
                        <span class="text-lg font-bold text-gray-900">${{ number_format($purchase->total_amount, 2) }}</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-gray-200">
                        <span class="text-lg font-semibold text-gray-700">{{ __('Paid Amount') }}:</span>
                        <span class="text-lg font-bold text-green-600">${{ number_format($purchase->paid_amount ?? 0, 2) }}</span>
                    </div>
                    <div class="flex justify-between items-center py-2">
                        <span class="text-lg font-semibold text-gray-700">{{ __('Remaining') }}:</span>
                        <span class="text-lg font-bold {{ ($purchase->remaining_amount ?? 0) > 0 ? 'text-red-600' : 'text-green-600' }}">
                            ${{ number_format($purchase->remaining_amount ?? 0, 2) }}
                        </span>
                    </div>
                    <div class="flex justify-between items-center py-3 border-t-2 border-gray-300">
                        <span class="text-xl font-bold text-gray-900">{{ __('Grand Total') }}:</span>
                        <span class="text-xl font-bold text-indigo-600">${{ number_format($purchase->total_amount, 2) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Section -->
    @if($purchase->status !== 'cancelled' && ($purchase->remaining_amount ?? 0) > 0)
        <div class="bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl rounded-2xl border border-gray-200/50">
            <div class="bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4">
                <h3 class="text-lg font-bold text-white">{{ __('Add Payment') }}</h3>
            </div>
            <div class="p-6">
                <form action="{{ route('admin.transactions.purchases.add-payment', $purchase) }}" method="POST" class="flex items-end space-x-4 {{ app()->getLocale() === 'ar' ? 'space-x-reverse' : '' }}">
                    @csrf
                    <div class="flex-1">
                        <label for="amount" class="block text-sm font-semibold text-gray-700 mb-2">{{ __('Payment Amount') }}</label>
                        <input type="number" name="amount" id="amount" step="0.01" min="0.01" max="{{ $purchase->remaining_amount }}" 
                               placeholder="0.00" required
                               class="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-green-500 focus:ring-4 focus:ring-green-500/20 transition-all duration-200">
                    </div>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-xl transition duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        {{ __('Add Payment') }}
                    </button>
                </form>
                <p class="text-sm text-gray-500 mt-2">{{ __('Maximum amount') }}: ${{ number_format($purchase->remaining_amount, 2) }}</p>
            </div>
        </div>
    @endif
</div>
@endsection
